package com.bukuwarung.edc

import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.app.config.NetworkConfig
import com.bukuwarung.edc.app.config.types.AppEnv
import com.bukuwarung.edc.app.config.types.AppId
import com.bukuwarung.edc.app.config.types.AppVariant
import com.bukuwarung.edc.app.config.types.AppVersion
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.homepage.ui.home.FakeLoginConfig
import com.bukuwarung.edc.homepage.ui.home.FakeHomeConfig
import com.bukuwarung.edc.homepage.ui.home.FakeVariantConfig
import com.bukuwarung.edc.util.EncryptedPreferencesHelper

abstract class RoboEdcApplication : EdcApplication() {
    override fun appInitialization() {
        val env = AppEnv.DEV
        AppConfig.Companion.current = AppConfig(
            appVariant = AppVariant.BUKU,
            appId = AppId("com.bukuwarung.edc.test"),
            appVersion = AppVersion(100),
            networkConfig = NetworkConfig(
                apiBaseUrl = "https://api-dev.bukuwarung.com/",
                env = env
            ),
            env = env,
            variantConfig = FakeVariantConfig(),
            homeConfig = FakeHomeConfig(),
            loginConfig = FakeLoginConfig()
        )
        EncryptedPreferencesHelper.initializeRobo(this)
    }
}
