<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_warranty_gradient"
    android:orientation="horizontal"
    android:paddingBottom="@dimen/_16dp">

    <ImageView
        android:id="@+id/ivImage"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginVertical="@dimen/_8dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        android:src="@drawable/ic_shield"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_8dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_weight="8"
        android:text="@string/extra_warranty_edc_bt_card_reader_peace_of_mind"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        app:layout_constraintEnd_toStartOf="@id/btnRedirect"
        app:layout_constraintStart_toEndOf="@id/ivImage"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:src="@drawable/ic_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/btnRedirect"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@drawable/bg_yellow_10_round_rectangle"
        android:backgroundTint="#89DE44"
        android:gravity="center"
        android:padding="@dimen/_4dp"
        android:text="@string/beli_sekarang"
        android:textColor="#1C3D93"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>