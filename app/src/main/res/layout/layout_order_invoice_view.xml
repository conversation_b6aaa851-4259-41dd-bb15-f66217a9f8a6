<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_payment_invoice"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!--    Store details    -->
    <include
        android:id="@+id/layout_store_detail"
        layout="@layout/layout_store_detail" />

    <!--    Invoice detail container    -->
    <LinearLayout
        android:id="@+id/ll_invoice_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:orientation="vertical"
        tools:background="@color/black_10"
        tools:layout_height="96dp" />

    <!--    Footer    -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_invoice_footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:paddingVertical="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_app_logo"
            android:layout_width="64dp"
            android:layout_height="32dp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_footer_link"
            app:layout_constraintEnd_toStartOf="@id/tv_footer_message"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_footer_message"
            app:srcCompat="@drawable/buku_agen_receipt" />

        <TextView
            android:id="@+id/tv_footer_message"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="38dp"
            android:text="@string/made_with_app"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tv_footer_link"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/img_app_logo" />

        <TextView
            android:id="@+id/tv_footer_link"
            style="@style/Body4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:text="@string/bukuwarung_url"
            app:layout_constraintStart_toEndOf="@id/img_app_logo"
            app:layout_constraintTop_toBottomOf="@id/tv_footer_message" />

        <TextView
            android:id="@+id/tv_pending_transaction_footer"
            style="@style/Body3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_8dp"
            android:lineHeight="@dimen/_18dp"
            android:text="@string/pending_card_transaction_footer"
            android:textAlignment="center"
            android:textColor="@color/colorPrimary"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_footer_link" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>