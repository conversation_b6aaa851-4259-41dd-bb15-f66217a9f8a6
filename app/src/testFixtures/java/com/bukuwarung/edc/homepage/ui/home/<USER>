package com.bukuwarung.edc.homepage.ui.home

//noinspection SuspiciousImport
import android.R
import android.util.Size
import com.bukuwarung.edc.app.config.VariantConfig

class FakeVariantConfig : VariantConfig {
    override var printerHeaderSize: Size = Size(384, 60)
    override var variantIdentifier: String = "FAKE_VARIANT"
    override var appFlow: String = "FAKE_FLOW"
    override var isPartnershipForOrderHistory: Boolean = false
    override var isPartnershipForAnalytics: Boolean = false
    override var isPartnershipForKycRedirection: Boolean = false
    override var isPartnershipForBluetoothBridge: Boolean = false
    override var settingsApiOrderFlowType: String = "NON_PARTNERSHIP"
    override var isPartnershipForTransactionHistory: Boolean = false
    override var defaultCommunicationChannel: String = "SMS"
    override var shouldShowHelp: Boolean = true
    override var shouldShowFooterLogo: Boolean = true
    override var shouldShowSwitchOtpChannelButton: Boolean = true
    override var primaryColorRes: Int = R.color.holo_blue_bright
    override var cardErrorDialogColorRes: Int = R.color.holo_red_light
    override var logoRes: Int = R.drawable.ic_dialog_info
    override var receiptLogoRes: Int = R.drawable.ic_dialog_info
    override var receiptHeaderText: String = "Fake Receipt Header"
    override var shouldShowBusinessAddress: Boolean = true
    override var shouldApplyGradientBackground: Boolean = false
    override var shouldShowTransactionFooter: Boolean = true
    override var shouldAddWatermark: Boolean = false
    override var shouldShowReceiptBranding: Boolean = true
    override var shouldUsePartnerPendingMessagesForTransaction: Boolean = false
    override var shouldHideChangePinOption: Boolean = false
    override var shouldHighlightDeviceStatusInSettings: Boolean = true
    override var shouldCheckPinLength: Boolean = true
    override var shouldPerformUserOnboarding: Boolean = true
    override var shouldShowTickerFragment: Boolean = false
    override var shouldHideToolbarMenu: Boolean = false
    override var shouldShowKomisiAgenDialog: Boolean = false
    override var shouldShowDateFilterInHistory: Boolean = true
    override var shouldShowWarrantyConfig: Boolean = true
    override var apiUrlSuffix: String = "fake"
    override var tncUrl: String = "https://fake.com/tnc"
    override var privacyPolicyUrl: String = "https://fake.com/privacy"
    override var shouldShowSakuDeviceMessage: Boolean = false
    override var shouldRestrictNonSakuDevices: Boolean = false
    override var shouldShowDeviceActivationBS: Boolean = false
    override var shouldShowContactCustomerCare: Boolean = true
    override var shouldShowTransferMoneyHelpButton: Boolean = true
    override var shouldShowBankAccountHelpButton: Boolean = true
    override var analyticsAppFlow: String = "FAKE_ANALYTICS_FLOW"
    override var analyticsCommunicationChannel: String = "SMS"
    override var analyticsOrderChannel: String = "FAKE_CHANNEL"
    override var shouldShowOrderValue: Boolean = true
    override var shouldShowOrderWarning: Boolean = true
    override var shouldShowActivationStepFour: Boolean = true
    override var orderCompletionBehavior: String = "DEFAULT"
    override var shouldShowExternalPinpadHelpButton: Boolean = true
    override var bankAccountColorRes: Int = R.color.holo_blue_bright
    override var bankAccountPrimaryColor: Int = 0xFF007BFF.toInt()
    override var shouldHideOrderHelpButton: Boolean = false
    override var shouldHideGradientBackground: Boolean = false
    override var shouldShowOrderBillingDetails: Boolean = true
    override var edcOrderDetailsVariantType: String = "NON_PARTNERSHIP"
    override var orderKycRedirectionUrl: String = "https://fake.com/kyc"
    override var orderKybRedirectionUrl: String = "https://fake.com/kyb"
    override var shouldShowStepThree: Boolean = true
    override var stepFourNumber: String = "4"
    override var shouldShowRefundForRejectedOrders: Boolean = false
    override var shouldShowRefundHelpButton: Boolean = true
    override var shouldActivateViaSaku: Boolean = false
    override var shouldUseAndroidActivation: Boolean = true
    override var shouldRedirectToLogin: Boolean = false
    override var shouldShowWarrantyNudge: Boolean = true
    override var shouldShowDeviceInfo: Boolean = true
    override var shouldShowBuyEdcButton: Boolean = true
    override var shouldHideFiltersForOrders: Boolean = false
    override var historyScreenTitle: String = "Fake History"
    override var shouldShowMoneyTransferHelpButton: Boolean = true
    override var transactionHistoryDeviceLabel: Int = R.string.ok
    override var shouldUseKycFlow: Boolean = true
    override var shouldHideSettlementMenu: Boolean = false
    override var shouldReactivateTerminal: Boolean = false
    override var shouldWaitForOpsOnDeeplink: Boolean = false
    override var shouldCheckOrderStatusOnDeeplink: Boolean = false
    override var orderInvoiceLogo: Int = R.drawable.ic_dialog_info
    override var shouldShowOrderInvoiceFooter: Boolean = true
    override var shouldShowEducationInSelectBank: Boolean = true
    override var shouldShowBankAccountEducation: Boolean = true
    override var cardErrorContactText: Int = R.string.ok
    override var cardErrorButtonColor: Int = R.color.holo_blue_bright
    override var cardErrorTextColor: Int = R.color.black
    override var usesMiniAtmReceiptLogic: Boolean = false
    override var shouldShowBersamaFooterInReceipt: Boolean = true
    override var receiptBusinessAddress: String = "Fake Business Address"
    override var tickerFragmentName: String = "ticker_fragment_fake"
    override var shouldShowPromoCodeInHomeTile: Boolean = false
    override var isPartnershipForProfile: Boolean = false
    override var profileAnalyticsFlow: String = "FAKE_PROFILE_FLOW"
    override var shouldHideSettlementToolbar: Boolean = false
    override var shouldShowAccountEducation: Boolean = true
    override var shouldShowSettlementMenu: Boolean = true
    override var shouldShowCardErrorEducation: Boolean = true
    override var shouldUseDefaultVariantForHistoryFilter: Boolean = false
    override var verifyOtpAnalyticsFlow: String = "FAKE_VERIFY_OTP_FLOW"
    override var shouldCheckOnboardingOnLogin: Boolean = true
    override var cardActivationType: String = "ANDROID"
}
