package com.bukuwarung.edc.homepage.ui.home

import com.bukuwarung.edc.app.config.HomeConfig

/**
 * Fake HomeConfig implementation for testing
 * Provides configurable home configuration for unit tests
 */
class FakeHomeConfig : HomeConfig {
    override var appDisplayName: String = "Fake EDC App"
    override var defaultStoreName: String = "Test Store"
    override var shouldShowLogo: Boolean = true
    override var homeShouldPerformAccountSetup: Boolean = true
    override var homeShouldCreateSaldoAccount: Boolean = true
    override var homeShouldUseDynamicBusinessTitle: Boolean = false
    override var homeShouldHandlePartnerOrderDetails: Boolean = false
    override var homepageSchemaFailsafeKey: String = HOMEPAGE_SCHEMA_FAILSAFE_TEST
    override var homeShouldShowPartnerActivationMessage: Boolean = false
    override var homeShouldShowActivationBottomSheet: Boolean = false
    override var shouldShowSettingsButton: Boolean = true
    override var isUsingMiniatmProTickerStyling: Boolean = false
    override var homepageSchemaKey: String = EDC_HOMEPAGE_SCHEMA_TEST

    override val remoteConfigDefaults: Map<String, Any>
        get() = mapOf(
            EDC_HOMEPAGE_SCHEMA_TEST to HOMEPAGE_SCHEMA_VAL_TEST,
            HOMEPAGE_SCHEMA_FAILSAFE_TEST to HOMEPAGE_SCHEMA_VAL_FAILSAFE_TEST
        )

    companion object {
        // Test schema keys
        const val EDC_HOMEPAGE_SCHEMA_TEST = "edc_homepage_schema_test"
        const val HOMEPAGE_SCHEMA_FAILSAFE_TEST = "homepage_schema_failsafe_test"

        // Test schema default values (minimal for testing)
        const val HOMEPAGE_SCHEMA_VAL_TEST = """
        [
  {
    "block_name": "edc_homepage_saldo",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 1
  }
]
    """

        const val HOMEPAGE_SCHEMA_VAL_FAILSAFE_TEST = """
        [
  {
    "block_name": "edc_homepage_card",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 1
  }
]
    """
    }
}
