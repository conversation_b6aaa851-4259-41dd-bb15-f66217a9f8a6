name: External Release AtmPro [MF919]

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  build_and_sign:
    name: Build and Sign APK
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'jetbrains'
          java-version: '17'

      - name: Decode Keystore (mf919)
        run: echo "${{ secrets.ANDROID_KEYSTORE_PAX }}" | base64 --decode > keystore.jks

      - name: Build atmproProdMorefunRelease APK
        run: ./gradlew assembleAtmproProdMorefunRelease --stacktrace

      - name: List Build Outputs
        run: ls -R app/build/outputs/apk/atmproProdMorefun/release

      - name: Sign APK
        run: |
          APK_PATH="app/build/outputs/apk/atmproProdMorefun/release/app-atmpro-prod-morefun-release-unsigned.apk"
          SIGNED_APK_PATH="${APK_PATH%-unsigned.apk}-signed.apk"
          ALIGNED_APK_PATH="${SIGNED_APK_PATH%-signed.apk}-aligned.apk"
          FINAL_APK_PATH="${ALIGNED_APK_PATH%-aligned.apk}-final.apk"

          storepass="${{ secrets.ANDROID_KEYSTORE_PAX_PASSWORD }}"
          keypass="${{ secrets.ANDROID_KEYSTORE_PAX_KEY_PASSWORD }}"
          alias="${{ secrets.ANDROID_KEYSTORE_PAX_ALIAS }}"

          jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore keystore.jks \
            -storepass "$storepass" \
            -keypass "$keypass" \
            -signedjar "$SIGNED_APK_PATH" \
            "$APK_PATH" \
            "$alias"

          ${ANDROID_HOME}/build-tools/30.0.3/zipalign -v 4 "$SIGNED_APK_PATH" "$ALIGNED_APK_PATH"

          ${ANDROID_HOME}/build-tools/30.0.3/apksigner sign \
            --ks keystore.jks \
            --ks-pass pass:"$storepass" \
            --key-pass pass:"$keypass" \
            --ks-key-alias "$alias" \
            --out "$FINAL_APK_PATH" \
            "$ALIGNED_APK_PATH"

      - name: Upload APK Artifact
        uses: actions/upload-artifact@v4
        with:
          name: atmpro-morefun-prod-release-apk
          path: app/build/outputs/apk/atmproProdMorefun/release/*-final.apk
          overwrite: true