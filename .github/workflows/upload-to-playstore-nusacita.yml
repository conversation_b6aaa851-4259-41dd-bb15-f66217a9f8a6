name: NusaCita Playstore Release [EDC Saku]

on:
  workflow_dispatch:
    inputs:
      upload-track:
        description: 'Select the track to upload to'
        required: true
        default: 'internal'
        type: choice
        options:
          - internal
          - alpha
          - beta
          - in-app-sharing
      branch:
        description: 'Select the branch to use'
        required: false
        default: 'develop'

jobs:
  aab:
    name: Generate AAB
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: 17
          distribution: 'jetbrains'

      - name: Grant execute permission for Gradle
        run: chmod +x gradlew

      - name: Configure Gradle distribution
        run: sed -i -e 's/-all.zip/-bin.zip/' gradle/wrapper/gradle-wrapper.properties

      - name: Build App Bundle
        run: ./gradlew bundleNusacitaProdPlayRelease

      - name: Debug List build outputs
        run: ls -R app/build/outputs/

      - name: List AAB files
        run: |
          echo "Listing AAB files in the expected directory:"
          ls -R app/build/outputs/bundle || echo "No AAB files found!"

      - name: Sign prod app bundle
        id: sign
        uses: r0adkll/sign-android-release@v1
        with:
          releaseDirectory: app/build/outputs/bundle/nusacitaProdPlayRelease
          signingKeyBase64: ${{ secrets.SIGN_KEY_ATMPRO }}
          alias: ${{ secrets.ANDROID_KEYSTORE_ATMPRO_ALIAS }}
          keyStorePassword: ${{ secrets.ANDROID_KEYSTORE_ATMPRO_PASSWORD }}
          keyPassword: ${{ secrets.ANDROID_KEYSTORE_ATMPRO_KEY_PASSWORD }}

      - name: Upload prod app bundle
        uses: actions/upload-artifact@v4
        with:
          name: app-play-prod-release
          path: app/build/outputs/bundle/nusacitaProdPlayRelease/app-nusacita-prod-play-release.aab
          overwrite: true

      - name: Create service_account.json
        id: createServiceAccount
        run: echo '${{ secrets.SERVICE_ACCOUNT_JSON }}' > service_account.json

      - name: Deploy to Play Store
        id: deploy
        uses: r0adkll/upload-google-play@v1.0.15
        with:
          serviceAccountJson: service_account.json
          packageName: com.nusacita.edc
          releaseFiles: app/build/outputs/bundle/nusacitaProdPlayRelease/app-nusacita-prod-play-release.aab
          track: ${{ github.event.inputs.upload-track }}
          whatsNewDirectory: changelog/