name: External Release [Pa<PERSON>, Verifone, MF919]

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  build_and_sign:
    name: Build and Sign APKs
    runs-on: ubuntu-latest

    strategy:
      matrix:
        flavor: [ pax, veri, morefun ]
        env: [ prod ]
      fail-fast: false # for debugging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'jetbrains'
          java-version: '17'

      - name: Decrypt Keystore
        run: |
          if [ "${{ matrix.flavor }}" = "morefun" ] || [ "${{ matrix.flavor }}" = "pax" ]; then
            echo "${{ secrets.ANDROID_KEYSTORE_PAX }}" | base64 --decode > keystore.jks
          elif [ "${{ matrix.flavor }}" = "veri" ]; then
            echo "${{ secrets.ANDROID_KEYSTORE_VERIFONE }}" | base64 --decode > keystore.jks
          fi

      - name: Build APK
        run: |
          if [ "${{ matrix.flavor }}" = "morefun" ]; then
            ./gradlew assembleBukuProdMorefunRelease --stacktrace
          elif [ "${{ matrix.flavor }}" = "pax" ]; then
            ./gradlew assembleBukuProdPaxRelease --stacktrace
          elif [ "${{ matrix.flavor }}" = "veri" ]; then
            ./gradlew assembleBukuProdVeriRelease --stacktrace
          fi

      - name: List Build Outputs (Debug Step)
        run: ls -R app/build/outputs

      - name: Sign APK
        run: |
          if [ "${{ matrix.flavor }}" = "morefun" ]; then
            APK_PATH="app/build/outputs/apk/bukuProdMorefun/release/app-buku-prod-morefun-release-unsigned.apk"
          elif [ "${{ matrix.flavor }}" = "pax" ]; then
            APK_PATH="app/build/outputs/apk/bukuProdPax/release/app-buku-prod-pax-release-unsigned.apk"
          elif [ "${{ matrix.flavor }}" = "veri" ]; then
            APK_PATH="app/build/outputs/apk/bukuProdVeri/release/app-buku-prod-veri-release-unsigned.apk"
          fi

          SIGNED_APK_PATH="${APK_PATH%-unsigned.apk}-signed.apk"
          ALIGNED_APK_PATH="${SIGNED_APK_PATH%-signed.apk}-aligned.apk"
          FINAL_APK_PATH="${ALIGNED_APK_PATH%-aligned.apk}-final.apk"

          if [ "${{ matrix.flavor }}" = "morefun" ] || [ "${{ matrix.flavor }}" = "pax" ]; then
            storepass="${{ secrets.ANDROID_KEYSTORE_PAX_PASSWORD }}"
            keypass="${{ secrets.ANDROID_KEYSTORE_PAX_KEY_PASSWORD }}"
            alias="${{ secrets.ANDROID_KEYSTORE_PAX_ALIAS }}"
          elif [ "${{ matrix.flavor }}" = "veri" ]; then
            storepass="${{ secrets.ANDROID_KEYSTORE_VERIFONE_PASSWORD }}"
            keypass="${{ secrets.ANDROID_KEYSTORE_VERIFONE_KEY_PASSWORD }}"
            alias="${{ secrets.ANDROID_KEYSTORE_VERIFONE_ALIAS }}"
          fi

          # Find the correct build-tools version
          BUILD_TOOLS_VERSION=$(find $ANDROID_HOME/build-tools -name "zipalign" | head -1 | xargs dirname | xargs basename)
          echo "Using build-tools version: $BUILD_TOOLS_VERSION"
          
          
          jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore keystore.jks \
            -storepass "$storepass" \
            -keypass "$keypass" \
            -signedjar "$SIGNED_APK_PATH" \
            "$APK_PATH" \
            "$alias"

          # Align the APK
          $ANDROID_HOME/build-tools/$BUILD_TOOLS_VERSION/zipalign -v 4 "$SIGNED_APK_PATH" "$ALIGNED_APK_PATH"

          # Final signing with apksigner for morefun only, move for others
          if [ "${{ matrix.flavor }}" = "morefun" ]; then
            $ANDROID_HOME/build-tools/$BUILD_TOOLS_VERSION/apksigner sign \
              --ks keystore.jks \
              --ks-pass pass:"$storepass" \
              --key-pass pass:"$keypass" \
              --ks-key-alias "$alias" \
              --out "$FINAL_APK_PATH" \
              "$ALIGNED_APK_PATH"
          else
            mv "$ALIGNED_APK_PATH" "$FINAL_APK_PATH"
          fi

      - name: Upload APK as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: prod-${{ matrix.flavor }}-release-apk
          path: |
            app/build/outputs/apk/bukuProd${{ matrix.flavor == 'morefun' && 'Morefun' || matrix.flavor == 'pax' && 'Pax' || 'Veri' }}/release/*-final.apk
          overwrite: true

  create_release:
    name: Create GitHub Release and Tag
    runs-on: ubuntu-latest
    needs: build_and_sign

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Extract Version Name
        id: extract_version
        run: |
          VERSION_NAME=$(./gradlew -q printVersionName)
          echo "VERSION_NAME=$VERSION_NAME" >> $GITHUB_ENV
          echo "Version name: $VERSION_NAME"

      - name: Generate Changelog
        id: generate_changelog
        run: |
          git log $(git describe --tags --abbrev=0)..HEAD --pretty=format:"* %s" > CHANGELOG.md

      - name: Create Release Tag
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"
          git tag ${{ env.VERSION_NAME }}
          git push https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/${{ github.repository }} ${{ env.VERSION_NAME }}

      - name: Download APK Artifacts
        uses: actions/download-artifact@v4
        with:
          name: prod-${{ matrix.flavor }}-release-apk

      - name: Attach Build Artifacts to Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ env.VERSION_NAME }}
          files: |
            app/build/outputs/apk/bukuProd${{ matrix.flavor == 'morefun' && 'Morefun' || matrix.flavor == 'pax' && 'Pax' || 'Veri' }}/release/*-final.apk
          body_path: CHANGELOG.md
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}