name: Generate APK And Scan

on:
  workflow_dispatch:
    inputs:
      partner:
        description: 'Select the Partner flavor'
        required: true
        default: 'buku'
        type: choice
        options:
          - buku
          - atmpro
          - nusacita
      store:
        description: 'Select the Store flavor'
        required: true
        default: 'play'
        type: choice
        options:
          - play
          - veri
          - pax
          - morefun
      buildType:
        description: 'Select the Build type'
        required: true
        default: 'debug'
        type: choice
        options:
          - debug
          - release
      env:
        description: 'Select the Environment'
        required: true
        default: 'stg'
        type: choice
        options:
          - dev
          - stg
          - prod

jobs:
  generate_apk_and_scan:
    name: "Build APK ${{ github.event.inputs.partner }}-${{ github.event.inputs.store }}-${{ github.event.inputs.env }}-${{ github.event.inputs.buildType }}"
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'jetbrains'
          java-version: '17'

      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: ${{ runner.os }}-gradle-

      - name: Capitalize env and store
        id: set_case
        run: |
          echo "ENV_CAPITALIZED=$(echo '${{ github.event.inputs.env }}' | sed 's/.*/\u&/')" >> $GITHUB_ENV
          echo "STORE_CAPITALIZED=$(echo '${{ github.event.inputs.store }}' | sed 's/.*/\u&/')" >> $GITHUB_ENV

      - name: Build unsigned APK
        run: |
          ./gradlew assemble${{ github.event.inputs.partner }}${{ env.STORE_CAPITALIZED }}${{ env.ENV_CAPITALIZED }}${{ github.event.inputs.buildType }} --no-daemon

      - name: List APK files
        run: |
          echo "Listing APK files in the expected directory:"
          ls -R app/build/outputs/apk

      - name: Upload APK as artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ github.event.inputs.partner }}-${{ github.event.inputs.store }}-${{ github.event.inputs.env }}-${{ github.event.inputs.buildType }}-apk
          path: app/build/outputs/apk/${{ github.event.inputs.partner }}${{ env.STORE_CAPITALIZED }}${{ env.ENV_CAPITALIZED }}/${{ github.event.inputs.buildType }}/app-${{ github.event.inputs.partner }}-${{ github.event.inputs.store }}-${{ github.event.inputs.env }}-${{ github.event.inputs.buildType }}${{ github.event.inputs.buildType == 'release' && '-unsigned' || '' }}.apk
          overwrite: true

      - name: Set up Node.js for APK scanning
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install Node.js dependencies
        run: npm install

      - name: Install jadx
        run: |
          eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
          brew install jadx

      - name: Scan APK for forbidden tokens
        id: apk_scan
        continue-on-error: true
        run: |
          APK_PATH="app/build/outputs/apk/${{ github.event.inputs.partner }}${{ env.STORE_CAPITALIZED }}${{ env.ENV_CAPITALIZED }}/${{ github.event.inputs.buildType }}/app-${{ github.event.inputs.partner }}-${{ github.event.inputs.store }}-${{ github.event.inputs.env }}-${{ github.event.inputs.buildType }}${{ github.event.inputs.buildType == 'release' && '-unsigned' || '' }}.apk"
          npm run scan-apk -- \
            --apk "$APK_PATH" \
            --token-files scripts/scanner_configs/${{ github.event.inputs.partner }}-forbidden-tokens.txt \
            --output apk-scan-report.md

      - name: Upload APK scan report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ${{ github.event.inputs.partner }}-${{ github.event.inputs.store }}-${{ github.event.inputs.env }}-${{ github.event.inputs.buildType }}-scan-report
          path: apk-scan-report.md
          retention-days: 30