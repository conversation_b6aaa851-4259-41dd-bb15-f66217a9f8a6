{"rules": [{"filePathMatches": "*", "rulesPaths": "AGENTS.md"}], "mcpServers": {"mcp-atlassian-api": {"command": "npx", "args": ["mcp-remote", "https://mcp.atlassian.com/v1/sse", "51328"]}, "firebase": {"command": "npx", "args": ["-y", "firebase-tools@latest", "experimental:mcp"]}, "slack": {"command": "npx", "args": ["-y", "slack-mcp-server@latest", "--transport", "stdio"], "env": {"SLACK_MCP_XOXP_TOKEN": "*********************************************************"}}}, "mcpEnvFile": ".env"}