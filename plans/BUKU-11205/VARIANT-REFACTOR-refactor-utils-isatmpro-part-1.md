# Variant Refactoring Plan: Utils.isAtmPro() to VariantConfig

## Overview

Refactor the codebase to support three app variants: "atmpro", "buku", and "nusa". Currently,
`Utils.isAtmPro()` is used throughout the codebase with the implicit assumption that `!isAtmPro()`
means "buku". With the new "nusa" variant, this logic needs to be explicitly handled.

During the migration, "nusa" will behave exactly like "atmpro", but we need to architect it properly
for future customization.

## Architecture Changes

### 1. Expand VariantConfig Interface

Move variant-specific behaviors from hardcoded `Utils.isAtmPro()` checks into the `VariantConfig`
interface.

### 2. Create Variant-Specific Implementations

- `BukuVariantConfig`
- `AtmProVariantConfig`
- `NusaVariantConfig` (initially identical to AtmProVariantConfig)

### 3. Update Application Classes

Ensure each Application class properly configures its VariantConfig.

## Implementation Strategy

### Phase 1: Create VariantConfig Properties

Add properties to VariantConfig interface for all variant-specific behaviors currently controlled by
`Utils.isAtmPro()`:

```kotlin
interface VariantConfig {
    val printerHeaderSize: Size
    
    // Business logic properties
    val isPartnershipFlow: Boolean
    val entryPoint: String
    val orderChannel: String
    val communicationChannel: CommunicationChannel
    val appFlow: String
    val landingQuery: String
    
    // UI/UX properties  
    val showHelpButton: Boolean
    val showFooterLogo: Boolean
    val primaryColorRes: Int
    val logoRes: Int
    val businessNameSource: BusinessNameSource
    
    // Feature flags
    val shouldCheckPinLength: Boolean
    val shouldPerformUserOnboarding: Boolean
    val shouldShowTickerFragment: Boolean
    val shouldShowKomisiAgenDialog: Boolean
    val shouldHideToolbarMenu: Boolean
    
    // External service configs
    val apiUrlSuffix: String
    val tncUrl: String
    val privacyPolicyUrl: String
    
    // Activation flow
    val deviceActivationStrategy: DeviceActivationStrategy
    
    // Receipt/Printing
    val receiptHeaderText: String
    val shouldShowBusinessAddress: Boolean

   // Money transfer & payments
   val shouldShowContactCustomerCare: Boolean
   val shouldShowTransferMoneyHelpButton: Boolean
   val shouldShowBankAccountHelpButton: Boolean

   // Analytics & tracking
   val analyticsAppFlow: String
   val analyticsCommunicationChannel: String

   // Order & activation flow
   val shouldShowOrderValue: Boolean
   val shouldShowOrderWarning: Boolean
   val shouldShowActivationStepFour: Boolean
   val orderCompletionBehavior: String

   // External pinpad
   val shouldShowExternalPinpadHelpButton: Boolean

   // Webview & external links
   val webviewLandingParam: String

   // Bank account colors
   val bankAccountColorRes: Int
}
```

### Phase 2: Create Concrete Implementations

Create three concrete implementations with proper variant-specific values.

### Phase 3: Replace Utils.isAtmPro() Usage

Replace all 70+ usages of `Utils.isAtmPro()` with appropriate VariantConfig properties.

### Phase 4: Update Application Classes

Ensure BukuEdcApplication, AtmProEdcApplication, and NusaEdcApplication properly initialize their
VariantConfig.

### Phase 5: Complete Remaining Refactoring

#### Status Check

- Core infrastructure created (VariantConfig, concrete implementations)
- Major screens refactored (Home, Login, Settings, VerifyOTP, receipts)
- Remaining: 45 files with 89+ Utils.isAtmPro() usages

#### Remaining Files by Category

#### Business Logic & Data Layer (12 files)

1. `MoneyTransferConfirmationActivity.kt` - 1 usage
2. `OrderInvoice.kt` - 2 usages
3. `PaxReceiptPrinterFlow.kt` - 4 usages
4. `Analytics.kt` - 2 usages
5. `KycUtil.kt` - 1 usage
6. `TransactionHistoryAdapter.kt` - 1 usage
7. `CardTransactionHistoryActivity.kt` - 6 usages
8. `TerminalManagementRepository.kt` - 1 usage
9. `ImageUtils.kt` - 1 usage
10. `UtilReceiptPrinterFlow.kt` (MoreFun) - 3 usages
11. `UtilReceiptPrinterFlow.kt` (Verifone) - 2 usages
12. `TransactionHistoryPagingSource.kt` - 1 usage

#### UI Components & Activities (18 files)

1. `HomePageRemoteConfig.kt` - 3 usages
2. `DeeplinkHandlerActivity.kt` - 2 usages
3. `SplashActivity.kt` - 1 usage
4. `DeeplinkHandlerViewModel.kt` - 1 usage
5. `HomeTickerFragment.kt` - 1 usage
6. `ProfileViewModel.kt` - 2 usages
7. `AddSettlementBankAccountActivity.kt` - 1 usage
8. `CardPinFirebaseDynamicActivity.kt` - 1 usage
9. `DateFilterBottomSheet.kt` - 1 usage
10. `ChooseAccountTypeActivity.kt` - 1 usage
11. `ExternalPinpadActivity.kt` - 1 usage
12. `NoSakuDeviceRegisteredBS.kt` - 1 usage
13. `PaymentStatusActivity.kt` - 1 usage
14. `HomePageTileFragment.kt` - 2 usages
15. `WebviewActivity.kt` - 2 usages
16. `BankAccountView.kt` - 2 usages
17. `CardErrorDialog.kt` - 4 usages
18. `AddBankAccountMoneyTransferActivity.kt` - 1 usage

#### Complex Activities (4 files)

1. `EdcOrderDetailsActivity.kt` - 22 usages
2. `VerifyOtpViewModel.kt` - 1 usage
3. `SettlementAccountActivity.kt` - 1 usage
4. `EdcActivationFragment.kt` - 2 usages

#### ViewModels & Use Cases (3 files)

1. `LoginViewModel.kt` - 1 usage
2. `SettingViewModel.kt` - 1 usage

### Refactoring Strategy

#### Priority 1: Complex Files (High Impact)

- `EdcOrderDetailsActivity.kt` - Most usages, complex business logic
- `CardTransactionHistoryActivity.kt` - Transaction history logic
- `PaxReceiptPrinterFlow.kt` - Printer integration

#### Priority 2: Core Infrastructure

- `Analytics.kt` - App-wide tracking
- `HomePageRemoteConfig.kt` - Configuration logic
- `OrderInvoice.kt` - Receipt generation

#### Priority 3: UI Components

- All remaining UI activities and fragments
- Dialog and bottom sheet components

### Testing Requirements

1. **Regression Testing**: Ensure AtmPro and Buku behavior unchanged
2. **Nusa Validation**: Verify Nusa mirrors AtmPro exactly
3. **Unit Test Updates**: Update all mocked Utils.isAtmPro() calls
4. **Integration Testing**: Test complete user flows

## Post-Migration Benefits

1. **Explicit Variant Logic**: No more implicit assumptions
2. **Easy Customization**: New variants easy to add
3. **Centralized Configuration**: All variant logic in one place
4. **Type Safety**: Compile-time checking instead of runtime
5. **Testability**: Easy to mock and test variant behaviors
6. **Maintainability**: Clear separation of concerns

## Detailed Analysis of Current Usage

### Pattern Categories:

1. **Business Logic (30 usages)**
    - Entry point determination: "MINIATMPRO" vs "BUKUAGEN"
    - Order channel: "MiniATMPro" vs "BUKUAGEN"
    - Flow type: "PARTNERSHIP" vs "NON_PARTNERSHIP"
    - Communication channel: WA vs SMS

2. **UI/UX Customization (25 usages)**
    - Button visibility (help buttons, toolbar menus)
    - Logo selection: bersama_logo vs buku_agent_print
    - Color schemes: primaryColor vs yellow_60
    - Footer logo display

3. **Feature Toggles (10 usages)**
    - PIN length checking
    - User onboarding requirements
    - Komisi agen dialog
    - Ticker fragment display

4. **External Configuration (5 usages)**
    - API URLs and endpoints
    - Terms & Conditions URLs
    - Privacy Policy URLs

## Migration Strategy

### For Hilt Injectable Classes

```kotlin
@Inject
lateinit var variantConfig: VariantConfig

// Replace:
if (Utils.isAtmPro()) { ... } 
// With:
if (variantConfig.isPartnershipFlow) { ... }
```

### For Non-Hilt Classes

```kotlin
// Replace:
if (Utils.isAtmPro()) { ... }
// With: 
if (AppConfig.current.variantConfig.isPartnershipFlow) { ... }
```

## Testing Strategy

1. **Unit Tests**: Update all tests that mock `Utils.isAtmPro()`
2. **Integration Tests**: Test all three variants behave correctly
3. **Regression Testing**: Ensure existing atmpro and buku behavior unchanged
4. **Future Flexibility**: Verify nusa can be easily customized later

## Risk Mitigation

1. **Gradual Migration**: Replace usages incrementally, not all at once
2. **Backward Compatibility**: Keep `Utils.isAtmPro()` during transition period
3. **Comprehensive Testing**: Test each variant thoroughly
4. **Feature Flag**: Use feature flags for gradual rollout if needed

## Files to Modify

### Core Infrastructure (4 files)

- `VariantConfig.kt` - Expand interface
- `AppConfig.kt` - Update to include concrete implementations
- `AtmProEdcApplication.kt` - Set AtmProVariantConfig
- `BukuEdcApplication.kt` - Set BukuVariantConfig

### New Files to Create (4 files)

- `NusaEdcApplication.kt` - New application class
- `BukuVariantConfig.kt` - Buku-specific configuration
- `AtmProVariantConfig.kt` - AtmPro-specific configuration
- `NusaVariantConfig.kt` - Nusa-specific configuration (identical to AtmPro initially)

### Files with Heavy Usage (Top 10 by usage count)

1. `HomePageActivity.kt` - 15 usages
2. `TianyuReceiptPrinterFlow.kt` - 8 usages
3. `CardReceiptActivity.kt` - 6 usages
4. `TransactionDetailsActivity.kt` - 6 usages
5. `CardTransactionHistoryActivity.kt` - 6 usages
6. `LoginActivity.kt` - 5 usages
7. `SettingActivity.kt` - 4 usages
8. `HomePageRemoteConfig.kt` - 3 usages
9. `PaxReceiptPrinterFlow.kt` - 4 usages
10. `UtilReceiptPrinterFlow.kt` (MoreFun) - 3 usages

### Utility Updates (2 files)

- `Utils.kt` - Keep methods but mark as deprecated, add new helper methods
- `AppModule.kt` - Already provides VariantConfig via DI

## Success Criteria

1. ✅ All three variants (atmpro, buku, nusa) build successfully
2. ✅ Nusa behaves identically to atmpro initially
3. ✅ Buku behavior remains unchanged
4. ✅ AtmPro behavior remains unchanged
5. ✅ No hardcoded variant checks remain (except in VariantConfig implementations)
6. ✅ All unit tests pass
7. ✅ Future nusa customization is straightforward

## Timeline Estimation

- **Phase 1**: VariantConfig expansion (2 days)
- **Phase 2**: Create implementations (1 day)
- **Phase 3**: Replace usage patterns (5 days)
- **Phase 4**: Testing and validation (2 days)
- **Total**: ~10 days

## Post-Migration Benefits

1. **Explicit Variant Logic**: No more implicit assumptions
2. **Easy Customization**: New variants easy to add
3. **Centralized Configuration**: All variant logic in one place
4. **Type Safety**: Compile-time checking instead of runtime
5. **Testability**: Easy to mock and test variant behaviors
6. **Maintainability**: Clear separation of concerns