## BUKU-11402 — Plan: Adjust GitHub Workflows for Flavor Dimension Order Change

### Context

- The flavor dimensions order changed from `partner, store, env` to `partner, env, store`.
- This impacts Gradle task names, output directories, and artifact filenames referenced in GitHub
  Actions workflows.

### User's initial prompt

I just changed the order of flavor dimensions from `partner, store, env` to `partner, env, store`.
Now correct the GitHub workflows that depend on the previous ordering.

### Goal

Update all affected workflow steps so assemble/bundle task names and artifact paths match the new
order: partner → env → store.

### Impacted Workflows and Planned Changes

1) `.github/workflows/external-release-workflow.yml`
    - Keep build tasks (already correct):
        - `assembleBukuProdMorefunRelease`
        - `assembleBukuProdPaxRelease`
        - `assembleBukuProdVeriRelease`
    - Update unsigned APK paths to partner-env-store:
        - mf919:
          `app/build/outputs/apk/bukuProdMorefun/release/app-buku-prod-morefun-release-unsigned.apk`
        - pax:   `app/build/outputs/apk/bukuProdPax/release/app-buku-prod-pax-release-unsigned.apk`
        - veri:
          `app/build/outputs/apk/bukuProdVeri/release/app-buku-prod-veri-release-unsigned.apk`

2) `.github/workflows/external-release-workflow-atmpro.yml`
    - Task name: `assembleAtmproProdMorefunRelease`.
    - Output dir: `app/build/outputs/apk/atmproProdMorefun/release`.
    - APK path:
      `app/build/outputs/apk/atmproProdMorefun/release/app-atmpro-prod-morefun-release-unsigned.apk`.

3) `.github/workflows/generate-apk.yml`
    - Assemble: `assemble<partner><ENV><STORE><buildType>`.
    - Artifact path dir: `app/build/outputs/apk/<partner><ENV><STORE>/<buildType>/`.
    - Filename: `app-<partner>-<env>-<store>-<buildType>[-unsigned].apk`.

4) `.github/workflows/upload-to-playstore.yml` (Buku)
    - Bundle task: `bundleBukuProdPlayRelease`.
    - Release dir: `app/build/outputs/bundle/bukuProdPlayRelease`.
    - AAB file: `app-buku-prod-play-release.aab`.
    - Update deploy `releaseFiles` accordingly.

5) `.github/workflows/upload-to-playstore-atmpro.yml` (AtmPro)
    - Bundle task: `bundleAtmproProdPlayRelease`.
    - Release dir: `app/build/outputs/bundle/atmproProdPlayRelease`.
    - AAB file: `app-atmpro-prod-play-release.aab`.
    - Update deploy `releaseFiles` accordingly.

6) `.github/workflows/upload-to-playstore-nusacita.yml` (NusaCita)
    - Bundle task: `bundleNusacitaProdPlayRelease`.
    - Release dir: `app/build/outputs/bundle/nusacitaProdPlayRelease`.
    - AAB file: `app-nusacita-prod-play-release.aab`.
    - Update deploy `releaseFiles` accordingly.

7) `.github/workflows/generate-common-apk.yml`
    - Assemble order: `assemble<ENV><STORE><build-variant>`.
    - Note: This workflow omits partner in inputs. We keep current scope and only adjust ordering.

### Acceptance Criteria

- All listed workflows build using corrected task names.
- Artifact paths resolve under partner-env-store directories with expected filenames.

### Validation Plan

- Trigger manual runs for adjusted workflows (internal track where applicable).
- Verify `./gradlew tasks` includes new task names like `bundleBukuProdPlayRelease` and
  `assembleAtmproProdMorefunRelease`.

### Notes

+ `.github/workflows/external-release-workflow.yml` has a non-matrix `create_release` job
  referencing `matrix.flavor` (pre-existing). Not changed here.