# BUKU-11402 — Setup & Refactor Firebase for all Apps

Ticket: BUKU-11402 — Setup & Refactor Firebase for all Apps  
Status: In Progress

## User's initial prompt

"see this jira ticket. @https://bukuwarung.atlassian.net/browse/BUKU-11402
create a branch for it's development and create a plan to solve it"

## Summary

Refactor Firebase setup across all app variants to remove the custom Gradle task that copies
`google-services.json` when switching variants, and adopt the official multi-project/multi-variant
configuration per Firebase guidelines (
see: https://firebase.google.com/docs/projects/multiprojects). Additionally, set up Firebase for the
Nusacita app.

## Goals

- Remove custom copying of `google-services.json` during builds.
- Configure per-variant Firebase files so each flavor/buildType resolves the correct Firebase
  project automatically.
- Ensure Crashlytics, Analytics, App Check, and Performance Monitoring continue to function across
  all variants after refactor.
- Add/verify Firebase setup for Nusacita.

## Non-Goals

- No new runtime features beyond Firebase configuration.
- No dependency additions unless strictly required by Firebase plugin guidelines.

## Assumptions / Open Questions

- Nusacita target details: package id(s), flavor mapping, and Firebase project(s) to use. Await
  confirmation of:
    - ApplicationId(s) for Nusacita variants
    - Corresponding Firebase project IDs and `google-services.json` files
- Current flavors: partner (`buku`, `atmpro`), store (`play`, `veri`, `pax`, `morefun`), env (`dev`,
  `stg`, `prod`). Verify which combinations are actually shipped and need Firebase.

## Current State (Key Observations)

- `app/build.gradle.kts` contains an `afterEvaluate` hook that copies
  `src/$env/google-services.json` to project root before Google Services tasks run. This is brittle
  and requires manual switching.
- Multiple product flavors imply multiple `applicationId`s per variant; each should have its own
  Firebase app config.

## Proposed Approach

1. Adopt official per-variant file placement for Google Services plugin:
    - Place `google-services.json` files under `app/src/<flavor><BuildType>/` or `app/src/<flavor>/`
      and/or `app/src/<buildType>/` so the plugin resolves the correct file automatically per
      variant.
    - For variants sharing the same Firebase app, reuse the same file via the most specific
      directory that applies to all needed variants.
2. Remove the custom `afterEvaluate` task that copies `google-services.json`.
3. Verify plugin application order remains correct (`com.google.gms.google-services` at the bottom
   of the module’s plugins block per guidelines).
4. Map each `applicationId` to a Firebase app:
    - Ensure every shipping variant has a registered Firebase app with a matching package name.
    - Download and place each variant’s `google-services.json` accordingly.
5. Nusacita setup:
    - Confirm Nusacita’s `applicationId` and target flavors.
    - Register app(s) in the proper Firebase project(s) and place `google-services.json`.
6. Validate Crashlytics/Perf/App Check after refactor:
    - Confirm initialization and expected Gradle tasks still run for all variants.
    - Ensure no task depends on copying files to project root.
7. CI/CD updates:
    - Remove any pipeline steps relying on the old copy behavior.
    - Ensure assemble tasks for required variants succeed without manual intervention.

## Detailed Steps

1. Inventory variants and applicationIds from `app/build.gradle.kts`.
2. For each env (`dev`, `stg`, `prod`) and relevant partner/store combinations, list the effective
   `applicationId`.
3. For each unique `applicationId`:
    - Verify a corresponding Firebase app exists; if missing, create it.
    - Download the `google-services.json`.
    - Place the file under the appropriate `app/src/<variant>` directory.
4. Delete the `afterEvaluate` copy block from `app/build.gradle.kts`.
5. Build matrix locally: assemble key variants across envs to confirm plugin resolution works.
6. Validate runtime integration (Crashlytics/Perf) on dev build.
7. Update docs (`AGENTS.md` or a new README section) describing where config files live and how to
   add new ones.

## Testing Plan

- Build Validation:
    - Run `./gradlew :app:assembleDev` for representative variants.
    - Run `./gradlew :app:assembleStg` and `:app:assembleProd` as applicable.
    - Ensure no “missing google-services.json” errors.
- Crashlytics/Perf Sanity:
    - Confirm crashlytics plugin tasks execute for each variant.
    - Perform local run on a dev variant; check logs for Firebase initialization.
- Unit Tests:
    - Run `./gradlew allUnitTest` to ensure no regressions.

## Risks & Mitigations

- Risk: Missing `google-services.json` for some variants → Mitigate by auditing all shipped variants
  and documenting required files.
- Risk: CI depends on the old copy behavior → Update CI to rely solely on per-variant files.

## Rollback Plan

- Revert the removal of `afterEvaluate` block and restore prior behavior if critical build failures
  occur.

## Deliverables

- Per-variant Firebase config files placed under `app/src/...`.
- Cleaned `app/build.gradle.kts` without the copy hook.
- Documentation updates.

## Confirmation Needed

- Nusacita `applicationId`(s), env coverage, and Firebase project mapping.
- Approval to proceed with removing the copy hook and placing per-variant configs.

## Firebase Config Mapping (Working Rules)

- Partner `atmpro`:
    - Use `atmpro` Firebase apps per env (`dev`/`stg`/`prod`).
    - For store `veri`, use `veri`-specific Firebase config; for other stores (`play`, `pax`,
      `morefun`), reuse the partner/env config.
- Partner `buku`:
    - Use `buku` Firebase apps per env; store `veri` has its own config, others reuse partner/env.
- Partner `nusacita` (temporary):
    - Reuse `atmpro` Firebase configs per env; `veri` store still uses the `veri`-specific config if
      applicable.

Suggested placement examples (actual files to be provided/confirmed):

- `app/src/atmproDev/google-services.json`
- `app/src/atmproStg/google-services.json`
- `app/src/atmproProd/google-services.json`
- `app/src/bukuDev/google-services.json`, `app/src/bukuStg/google-services.json`,
  `app/src/bukuProd/google-services.json`
- `app/src/veriDev/google-services.json`, `app/src/veriStg/google-services.json`,
  `app/src/veriProd/google-services.json`
- `app/src/nusacitaDev/google-services.json` → temporarily symlink/copy same as `atmproDev`