# Extract HomePageActivity Specific Fields into HomeConfig Interface

## Initial User Prompt
Move the HomePageActivity specific fields into a new interface HomeConfig.

## Overview
Extract HomePageActivity-specific configuration fields from `VariantConfig` interface into a new dedicated `HomeConfig` interface. This will improve separation of concerns and make the configuration more modular.

**Important:** `HomeConfig` and `VariantConfig` will be completely separate interfaces with no
inheritance relationship. This is a full migration.

## Analysis

### HomePageActivity Specific Fields (from VariantConfig)
The following fields are specifically related to HomePageActivity behavior:
- `appDisplayName: String` - Display name for the app
- `shouldShowLogo: Boolean` - Controls ivLogo and vwSeparator visibility
- `homeShouldPerformAccountSetup: Boolean` - Account setup behavior
- `homeShouldCreateSaldoAccount: Boolean` - Saldo account creation
- `homeShouldUseDynamicBusinessTitle: Boolean` - Dynamic business title
- `homeShouldHandlePartnerOrderDetails: Boolean` - Partner order details handling
- `homeShouldUsePartnerSchemaFallback: Boolean` - Partner schema fallback
- `homeShouldShowPartnerActivationMessage: Boolean` - Partner activation message
- `homeShouldShowActivationBottomSheet: Boolean` - Activation bottom sheet
- `shouldShowSettingsButton: Boolean` - Settings button on card reader
- `isUsingMiniatmProTickerStyling: Boolean` - Ticker styling

## Implementation Plan

### 1. Create HomeConfig Interface
- Create new file: `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/HomeConfig.kt`
- Define interface with all HomePageActivity specific fields
- Add KDoc documentation

### 2. Update VariantConfig Interface

- **REMOVE** all HomePageActivity specific fields from `VariantConfig` interface
- Keep all other fields intact
- No inheritance relationship with `HomeConfig`

### 3. Create Separate HomeConfig Implementations

Create separate HomeConfig implementation classes alongside each variant config:

- `BukuHomeConfig` - Implements `HomeConfig` with Buku-specific values
- `AtmProHomeConfig` - Implements `HomeConfig` with AtmPro-specific values
- `NusaHomeConfig` - Implements `HomeConfig` with Nusa-specific values
- `FakeHomeConfig` - Test fixture implementing `HomeConfig`

### 4. Update Variant Config Implementations

Update existing variant config classes to remove HomeConfig fields:

- `BukuVariantConfig` - Remove all HomeConfig fields
- `AtmProVariantConfig` - Remove all HomeConfig fields
- `NusaVariantConfig` - Remove all HomeConfig fields
- `FakeVariantConfig` - Remove all HomeConfig fields

### 5. Update AppConfig Class

- Add new field: `homeConfig: HomeConfig`
- Update all Application classes to instantiate and provide both configs:
    - `BukuEdcApplication` - Provide `BukuHomeConfig()`
    - `AtmProEdcApplication` - Provide `AtmProHomeConfig()`
    - `NusaEdcApplication` - Provide `NusaHomeConfig()`
    - `NusacitaEdcApplication` - Provide `NusaHomeConfig()` (reuse Nusa config)
    - `RoboEdcApplication` - Provide `FakeHomeConfig()`

### 6. Update Dependency Injection (AppModule)
- Add provider function for `HomeConfig` in `AppModule`
- The provider will extract `HomeConfig` from `AppConfig.homeConfig`
- This allows components to inject `HomeConfig` directly instead of full `VariantConfig` when they
  only need home-specific configuration

### 7. Find and Update All Existing Usages
- Search for all usages of the HomePageActivity-specific fields across the codebase
- Update classes/components that use these fields to inject `HomeConfig` instead of `VariantConfig`
- This improves code clarity by making dependencies more explicit
- Files likely to be affected:
    - HomePageActivity and related ViewModels
    - Any fragments or components using home configuration
    - Test files that mock or fake these configurations

### 7. Find and Update All Existing Usages
Based on codebase analysis, the following files need to be updated to use `HomeConfig`:

**Production Code:**

- `app/src/main/java/com/bukuwarung/edc/homepage/ui/home/<USER>
    - Uses: appDisplayName, shouldShowLogo, homeShouldPerformAccountSetup,
      homeShouldCreateSaldoAccount,
      homeShouldUseDynamicBusinessTitle, homeShouldHandlePartnerOrderDetails,
      homeShouldUsePartnerSchemaFallback,
      homeShouldShowPartnerActivationMessage, homeShouldShowActivationBottomSheet,
      shouldShowSettingsButton
    - Action: Change constructor/field injection from `VariantConfig` to `HomeConfig`

- `app/src/main/java/com/bukuwarung/edc/homepage/ui/home/<USER>
    - Uses: homeShouldHandlePartnerOrderDetails
    - Action: Change constructor/field injection from `VariantConfig` to `HomeConfig`

- `app/src/main/java/com/bukuwarung/edc/homepage/ui/ticker/HomeTickerFragment.kt`
    - Uses: isUsingMiniatmProTickerStyling
    - Action: Change constructor/field injection from `VariantConfig` to `HomeConfig`

**Note:** These classes should inject `HomeConfig` instead of full `VariantConfig` since they only
use home-specific fields.

### 8. Verify Changes
- Run `./gradlew allUnitTest` to ensure no existing tests break
- Verify that all variant configurations compile correctly
- Check that HomePageActivity can still access all required fields

## Files to Create
1. `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/HomeConfig.kt`
2. `app/src/main/java/com/bukuwarung/edc/app/config/home/<USER>
3. `app/src/main/java/com/bukuwarung/edc/app/config/home/<USER>
4. `app/src/main/java/com/bukuwarung/edc/app/config/home/<USER>
5. `app/src/testFixtures/java/com/bukuwarung/edc/homepage/ui/home/<USER>

## Files to Modify

1. `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/VariantConfig.kt` - Remove
   HomeConfig fields
2. `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/AppConfig.kt` - Add
   homeConfig field
3. `app/src/main/java/com/bukuwarung/edc/app/config/variants/BukuVariantConfig.kt` - Remove
   HomeConfig fields
4. `app/src/main/java/com/bukuwarung/edc/app/config/variants/AtmProVariantConfig.kt` - Remove
   HomeConfig fields
5. `app/src/main/java/com/bukuwarung/edc/app/config/variants/NusaVariantConfig.kt` - Remove
   HomeConfig fields
6. `app/src/testFixtures/java/com/bukuwarung/edc/homepage/ui/home/<USER>
   HomeConfig fields
7. `app/src/main/java/com/bukuwarung/edc/di/AppModule.kt` - Add HomeConfig provider
8. `app/src/buku/java/com/bukuwarung/edc/global/BukuEdcApplication.kt` - Provide BukuHomeConfig
9. `app/src/atmpro/java/com/bukuwarung/edc/global/AtmProEdcApplication.kt` - Provide
   AtmProHomeConfig
10. `app/src/nusa/java/com/bukuwarung/edc/global/NusaEdcApplication.kt` - Provide NusaHomeConfig
11. `app/src/nusacita/java/com/bukuwarung/edc/global/NusacitaEdcApplication.kt` - Provide
    NusaHomeConfig
12. `app/src/test/java/com/bukuwarung/edc/RoboEdcApplication.kt` - Provide FakeHomeConfig
13. `app/src/main/java/com/bukuwarung/edc/homepage/ui/home/<USER>
14. `app/src/main/java/com/bukuwarung/edc/homepage/ui/home/<USER>
15. `app/src/main/java/com/bukuwarung/edc/homepage/ui/ticker/HomeTickerFragment.kt` - Inject
    HomeConfig

## Testing Strategy
- Run `./gradlew allUnitTest` to ensure no existing tests break
- Verify that all variant configurations compile correctly
- Check that HomePageActivity can still access all required fields

## Benefits

1. **True Separation of Concerns** - HomeConfig and VariantConfig are completely independent
2. **Better Organization** - HomePage specific configurations are isolated in dedicated classes
3. **Clear Dependencies** - Components explicitly declare they need HomeConfig, not VariantConfig
4. **Maintainability** - Easier to understand and modify HomePage configurations
5. **Scalability** - Sets a clear pattern for extracting other activity-specific configs
6. **No Coupling** - Changes to HomeConfig don't affect VariantConfig and vice versa
