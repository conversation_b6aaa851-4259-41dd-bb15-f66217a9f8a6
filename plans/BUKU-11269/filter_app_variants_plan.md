# Filter App Variants Plan - BUKU-11269

## Initial User Prompt
I want to filter the app variants to enable only:
- for partner at<PERSON><PERSON>, only store play and morefun
- for partner n<PERSON><PERSON><PERSON>, only store play
- for partner buku, enabled on all stores

## Implementation Plan
Add beforeVariants block in androidComponents to filter invalid variant combinations.

## Implementation Status
- [ ] Add variant filter
- [ ] Test builds

## Test Results

### Atmpro Partner Tests
- ✅ assembleAtmproStgPlayDebug: BUILD SUCCESSFUL (enabled as expected)
- ✅ assembleAtmproStgMorefunDebug: BUILD SUCCESSFUL (enabled as expected)
- ✅ assembleAtmproStgVeriDebug: Task not found (disabled as expected)
- ✅ assembleAtmproStgPaxDebug: Task not found (disabled as expected)

### Nusacita Partner Tests
- ✅ assembleNusacitaStgPlayDebug: BUILD SUCCESSFUL (enabled as expected)
- ✅ assembleNusacitaStgVeriDebug: Task not found (disabled as expected)
- ✅ assembleNusacitaStgMorefunDebug: Task not found (disabled as expected)

### Buku Partner Tests
- ✅ assembleBukuStgPlayDebug: BUILD SUCCESSFUL (enabled as expected)
- ✅ assembleBukuStgVeriDebug: BUILD SUCCESSFUL (enabled as expected)
- ✅ assembleBukuStgPaxDebug: BUILD SUCCESSFUL (enabled as expected)

## Implementation Status
- [x] Add variant filter in androidComponents block
- [x] Verify variant filtering works correctly
- [x] Test sample builds

## Summary
Successfully implemented variant filtering in app/build.gradle.kts using beforeVariants block.
The filtering correctly:
- Enables only play and morefun stores for atmpro partner
- Enables only play store for nusacita partner  
- Enables all stores for buku partner

All 21 valid variant combinations are now available while 15 invalid combinations are disabled.


## Update: Release Build Filter Added

### New Requirement
Release builds should only be available for prod environment (not dev or stg).

### Test Results
- ✅ assembleBukuStgPlayRelease: Task not found (disabled)
- ✅ assembleBukuDevPlayRelease: Task not found (disabled)
- ✅ assembleBukuProdPlayRelease: BUILD SUCCESSFUL (enabled)
- ✅ assembleAtmproProdPlayRelease: BUILD SUCCESSFUL (enabled)
- ✅ assembleBukuStgPlayDebug: BUILD SUCCESSFUL (debug still works)

### Final Variant Count
- Debug builds: 21 variants
- Release builds: 7 variants (prod only)
- Total: 28 build configurations (down from 72)
