<<'PATCH'
*** Begin Patch
*** Add File: plans/BUKU-XXXX-refactor-magic-strings.md
+# Refactor repeated magic strings into private constants

+

+## User prompt
+"refactor all of magic strings that are used more than once into a private constant"

+

+## Scope
+- File: `app/src/main/java/com/bukuwarung/edc/homepage/ui/home/<USER>

+

+## Goal
+- Replace all string literals that occur more than once in this file with private constants to
improve maintainability and reduce typo bugs.

+

+## Candidates (found repeated in the file)
+- `"IS_ACTIVATED"` → `private const val EXTRA_IS_ACTIVATED = "IS_ACTIVATED"`
+- `"onboarding_dialog_shown"` →
`private const val PREF_ONBOARDING_DIALOG_SHOWN = "onboarding_dialog_shown"`
+- `"type"` (Intent extra key) → `private const val EXTRA_TYPE = "type"`
+- `"url"` (Intent extra key) → `private const val EXTRA_URL = "url"`
+- `"no_device_registered_dialog"` (fragment tag) →
`private const val TAG_NO_DEVICE_REGISTERED_DIALOG = "no_device_registered_dialog"`
+- Use existing `PPOB` and `CARD` constants instead of repeating string literals "ppob" and "card"
in `scrollToView`.

+

+Notes:
+- User-visible strings (dialog titles, button texts, messages) are not included in this refactor as
they should live in string resources. This plan targets only technical keys/tags used multiple
times.

+

+## Implementation
+1. Add new private constants inside `HomePageActivity` `companion object` (as `private const val`)
or as private top-level constants within the file (scoped to the file). Prefer inside the
`companion object` for proximity and clarity with existing constants.
+2. Replace all occurrences of the above literals with the new constants.
+3. Replace the `"ppob"`/`"card"` literals in `scrollToView` with the already-declared `PPOB` and
`CARD` constants.
+4. Keep behavior identical; no logic changes.

+

+## Testing & Verification
+- Build the project and run unit tests:

+
    - `./gradlew allUnitTest`
      +- Manually verify:
+
    - Opening deeplinks that trigger `IS_ACTIVATED` flow still works.
+
    - Onboarding dialog preference still suppresses the dialog after first display.
+
    - Tapping tiles still routes correctly (Intent extras `type` and `url`).
+
    - The no-device-registered bottom sheet still shows (tag unchanged via constant usage).
+

+## Out of Scope / Known Items
+- Lint shows: Unresolved reference for `R.string.try_now` (line ~1125). This is not part of this
refactor. If desired, we can add a follow-up task to either create the string resource or replace it
with an existing one.

+

+## Rollback Plan
+- Revert the file to the previous revision if any runtime issues appear after refactor.

+

+## Request
+- Please confirm this plan. Once approved, I will implement the changes and run tests.

+

*** End Patch
PATCH