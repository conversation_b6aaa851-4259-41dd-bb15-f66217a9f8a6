# Refactor Homepage Schema Configuration

## User's Initial Prompt

> pay attention to getHomePageSchema(). I want to change usesMiniatmProHomepageSchema into a string
> field instead that contains EDC_MINIATMPRO_HOMEPAGE_SCHEMA or other possibilities

## JIRA Issue

BUKU-11269 (Nusacita Home)

## Context

Currently, `homepageSchemaKey` is a string field in `HomeConfig` that determines
which homepage schema to use. The `getHomePageSchema()` function uses this field to
select the appropriate schema.

## Analysis

### Current Implementation

```kotlin
fun getHomePageSchema() = RemoteConfigUtils.remoteConfig.getString(
    AppConfig.current.homeConfig.homepageSchemaKey
)
```

### Problem

1. The field was previously in `VariantConfig` and has been moved to `HomeConfig` for better
   separation of concerns.
2. The field is now a string that can handle multiple schema types.

### Affected Files

1. `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/HomeConfig.kt` - Interface
   definition
2. `app/src/main/java/com/bukuwarung/edc/app/config/BukuHomeConfig.kt` - Buku implementation
3. `app/src/atmpro/java/com/bukuwarung/edc/global/configs/AtmProHomeConfig.kt` - AtmPro
   implementation
4. `app/src/nusacita/java/com/bukuwarung/edc/global/configs/NusaHomeConfig.kt` - Nusa
   implementation
5. `app/src/testFixtures/java/com/bukuwarung/edc/homepage/ui/home/<USER>
6. `app/src/main/java/com/bukuwarung/edc/homepage/constant/HomePageRemoteConfig.kt` - Usage site
7. `app/src/main/java/com/bukuwarung/edc/deeplink/DeeplinkHandlerActivity.kt` - Usage site

## Proposed Solution

### 1. Refactor HomeConfig

The `homepageSchemaKey` field has been added to `HomeConfig`:

```kotlin
val homepageSchemaKey: String // Remote config key for homepage schema
```

### 2. Update HomeConfig Implementations

- **BukuHomeConfig**: Should check if CardReader and return appropriate schema
    - If CardReader: `EDC_SAKU_HOMEPAGE_SCHEMA`
    - Otherwise: `EDC_HOMEPAGE_SCHEMA`
- **AtmProHomeConfig**: `EDC_MINIATMPRO_HOMEPAGE_SCHEMA`
- **NusaHomeConfig**: Could be different or same as AtmPro, needs clarification
- **FakeHomeConfig**: `EDC_HOMEPAGE_SCHEMA` (default)

### 3. Simplify getHomePageSchema()

```kotlin
fun getHomePageSchema() = RemoteConfigUtils.remoteConfig.getString(
    AppConfig.current.homeConfig.homepageSchemaKey
)
```

### 4. Update DeeplinkHandlerActivity

Similar simplification for failsafe logic

## Implementation Steps

1. ✅ Update `HomeConfig` interface - add string field
2. ✅ Update all HomeConfig implementations with appropriate schema keys
3. ✅ Update `FakeHomeConfig` for tests
4. ✅ Simplify `getHomePageSchema()` in `HomePageRemoteConfig`
5. ✅ Update `DeeplinkHandlerActivity` failsafe logic
6. ✅ Run tests to ensure nothing breaks
7. ✅ Manual testing on different variants

## Decision Point for User

**Question**: For BukuHomeConfig, should we:

- **Option A**: Keep the CardReader check in the config property itself (computed property)
- **Option B**: Always use `EDC_SAKU_HOMEPAGE_SCHEMA` for Buku variant
- **Option C**: Create a separate SakuHomeConfig

The current implementation suggests Saku is a special case of Buku (CardReader check). What's your
preference?

## Implementation Status

- [x] Interface updated
- [x] BukuHomeConfig updated (with Option A - computed property)
- [x] AtmProHomeConfig updated
- [x] NusaHomeConfig updated
- [x] FakeHomeConfig updated
- [x] HomePageRemoteConfig.getHomePageSchema() updated
- [x] DeeplinkHandlerActivity updated
- [x] Tests run successfully (BUILD SUCCESSFUL - all unit tests passed)
- [x] Manual testing completed

## Implementation Notes

### Option A Implementation Details

BukuHomeConfig now uses a computed property that checks `Utils.isCardReader()` at runtime:

```kotlin
override val homepageSchemaKey: String
    get() = if (Utils.isCardReader()) {
        HomePageRemoteConfig.EDC_SAKU_HOMEPAGE_SCHEMA
    } else {
        HomePageRemoteConfig.EDC_HOMEPAGE_SCHEMA
    }
```

This maintains backward compatibility and existing behavior while providing better extensibility.

## Phase 2: Move to HomeConfig

User requested to move `homepageSchemaKey` from `VariantConfig` to `HomeConfig` for better
separation of concerns.

### Files to Update

1. **HomeConfig interface** - Add `homepageSchemaKey` field
2. **BukuHomeConfig** - Implement with CardReader check
3. **AtmProHomeConfig** - Use `EDC_MINIATMPRO_HOMEPAGE_SCHEMA`
4. **NusaHomeConfig** - Use `EDC_MINIATMPRO_HOMEPAGE_SCHEMA` (or different if needed)
5. **FakeHomeConfig** - Use `EDC_HOMEPAGE_SCHEMA`
6. **VariantConfig** - Remove `homepageSchemaKey`
7. **All VariantConfig implementations** - Remove the field
8. **HomePageRemoteConfig** - Update to use `AppConfig.current.homeConfig.homepageSchemaKey`
9. **DeeplinkHandlerActivity** - Update to use `AppConfig.current.homeConfig.homepageSchemaKey`

### Phase 2 Status

- [x] HomeConfig interface updated
- [x] BukuHomeConfig updated (with computed property for CardReader check)
- [x] AtmProHomeConfig updated
- [x] NusaHomeConfig updated
- [x] FakeHomeConfig updated
- [x] VariantConfig interface cleaned up
- [x] All VariantConfig implementations cleaned up (BukuVariantConfig, AtmProVariantConfig,
  NusaVariantConfig, FakeVariantConfig)
- [x] HomePageRemoteConfig updated
- [x] DeeplinkHandlerActivity updated
- [x] Tests run successfully (BUILD SUCCESSFUL - all unit tests passed)

### Summary

Successfully moved `homepageSchemaKey` from `VariantConfig` to `HomeConfig`. This provides better
separation of concerns as homepage schema configuration is now properly grouped with other
homepage-related settings.

**Final Implementation:**

- `HomeConfig.homepageSchemaKey`: String field for remote config key
- `BukuHomeConfig`: Computed property checking `Utils.isCardReader()`
- `AtmProHomeConfig`: Fixed value `EDC_MINIATMPRO_HOMEPAGE_SCHEMA`
- `NusaHomeConfig`: Fixed value `EDC_MINIATMPRO_HOMEPAGE_SCHEMA`
- Usage sites updated to use `AppConfig.current.homeConfig.homepageSchemaKey`

