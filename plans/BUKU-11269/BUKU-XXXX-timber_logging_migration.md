# Plan: Migrate logging to <PERSON><PERSON> in `HomePageActivity.kt`

## User Prompt

"change the logging here to use timber"

## Context

- Repository: Kotlin Android Studio project (EDC app with multiple target variants).
- Target file: `app/src/main/java/com/bukuwarung/edc/homepage/ui/home/<USER>
- Current logging approach mixes `android.util.Log` and existing helpers like `bwLog`. `Timber` is
already available (`import timber.log.Timber`).

## Objective

Replace all `android.util.Log` and `bwLog` usages with `<PERSON><PERSON>` in `HomePageActivity.kt` while
preserving log
semantics and tags.

## Scope

- In `HomePageActivity.kt` only:
  - Introduce a private tagged logger for the class:
    - `private val LOG_TAG = HomePageActivity::class.java.simpleName`
    - `private val logger: Timber.Tree get() = Timber.tag(LOG_TAG)`
    - Replace `Log.d("TAG", msg)` with `Timber.tag("TAG").d(msg)`.
    - Replace `Log.e("TAG", msg)` with `Timber.tag("TAG").e(msg)`.
    - Replace `Log.d(TAG, "... $value")`-style interpolations with Timber equivalents, using
      parameterized format when suitable: `Timber.tag(TAG).d("... %s", value)`.
  - Prefer `logger.d(...)` / `logger.e(...)` throughout this file to avoid repeating the tag.
  - Replace `bwLog("message")` with `logger.d("message")`.
  - Replace `bwLog("message", throwable)` with `logger.e(throwable, "message")`.
    - Remove the `import android.util.Log` if it becomes unused.
  - Remove the `import com.bukuwarung.edc.util.Utils.bwLog` if it becomes unused.

## Non-Goals

- No functional changes to business logic.
- No global search-and-replace in other files.
- No dependency changes.

## Implementation Steps

1. Update imports: ensure `import timber.log.Timber` is present; remove `import android.util.Log`
if unused.
2. Migrate all `Log.d`, `Log.e` (and any other `Log.*` if present) to the appropriate `Timber`
   methods, preserving tags.
3. Add `LOG_TAG` and `logger` property and migrate usage to `logger.d/e`.
4. Migrate `bwLog` usages to `Timber` with correct severity (`d` for simple messages, `e` for
   messages with a `Throwable`).
5. Build to verify no compile errors.

## Acceptance Criteria

- `HomePageActivity.kt` contains no usages of `android.util.Log`.
- `HomePageActivity.kt` contains no usages of `bwLog`.
- All logging uses the `logger` property with the class tag.
- All previous logs have Timber equivalents with the same tag/severity.
- Module builds successfully.

## Rollback Plan

- Revert the commit that performs the migration in case of unforeseen issues.