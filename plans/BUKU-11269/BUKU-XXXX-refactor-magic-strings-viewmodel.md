<<'PATCH'
*** Begin Patch
*** Add File: plans/BUKU-XXXX-refactor-magic-strings-viewmodel.md
+# Refactor repeated magic strings into private constants (ViewModel)

## User prompt

"do the same refactoring here too"

## Scope

- File: `app/src/main/java/com/bukuwarung/edc/homepage/ui/home/<USER>

## Goal

- Replace all string literals that occur more than once with private constants to improve
  maintainability and reduce typo bugs.

## Candidates (repeated in the file)

- Preferences/Keys
    - `"saldo_account_created"` → `private const val PREF_SALDO_ACCOUNT_CREATED`
    - `"kyc_account_id"` → `private const val PREF_KYC_ACCOUNT_ID`
    - `"t_id"` → `private const val PREF_T_ID`
    - `"serial_number"` → `private const val PREF_SERIAL_NUMBER`
    - `"store_name"` → `private const val PREF_STORE_NAME`
    - `"payment_account_id"` → `private const val KEY_PAYMENT_ACCOUNT_ID`
    - `"is_working_key_loaded"` → `private const val PREF_IS_WORKING_KEY_LOADED`
    - `"user_id"` → `private const val PREF_USER_ID`
- Firestore
    - `"book_store"` → `private const val COLLECTION_BOOK_STORE`
- Logging/Analytics
    - `"device-list"` → `private const val LOG_TAG_DEVICE_LIST`
    - `"backend_tid"` → `private const val ANALYTICS_PROP_BACKEND_TID`
- Business defaults (non-UI text used as default data)
    - `"BukuAgen EDC"` → `private const val DEFAULT_STORE_NAME`

Notes:

- User-visible UI strings remain in resources; the defaults above are data values, not UI labels.
- Single-use strings are intentionally left inline to avoid over-constant-ization.

## Implementation

1. Add constants inside `HomePageViewModel` as `private const val` within a `companion object`.
2. Replace all occurrences of the above repeated literals with the new constants.
3. Keep behavior identical.

## Testing & Verification

- Build and run unit tests: `./gradlew allUnitTest`
- Manual checks where applicable:
    - Device list logs still print expected content
    - Shared preferences and encrypted preferences values remain consistent
    - Firestore collection writes still succeed

## Rollback Plan

- Revert the file if any runtime issues appear after refactor.

*** End Patch
PATCH