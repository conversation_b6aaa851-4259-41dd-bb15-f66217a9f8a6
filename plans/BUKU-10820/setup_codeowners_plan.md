# BUKU-10820: Setup CODEOWNERS
## Initial User Prompt
do @https://bukuwarung.atlassian.net/browse/BUKU-10820

## Issue Summary  
Setup CODEOWNERS file for the repository

## Acceptance Criteria
- Ask team what code sections they are confident of (DON<PERSON> via Slack)
- Add owners to GitHub CODEOWNERS file
- Commit the file

## Information Gathered
From JIRA Comments:
- Naupal Trianto: card package owner

Card-related directories found:
- app/src/main/java/com/bukuwarung/edc/card/
- bluetooth-devices-setup/src/main/java/com/bukuwarung/cardreader/

## Implementation Plan
1. Create .github/CODEOWNERS file
2. Add Naupal as card package owner
3. Add placeholders for other module owners
4. Commit with conventional commit format

## Status
- Plan created - awaiting confirmation
- Need GitHub usernames
- Need to clarify other module owners

## Implementation Completed

CODEOWNERS file created at .github/CODEOWNERS with the following mappings:
- Andika: gradle files, .github/, network_security_config.xml
- Bhargav: payments/, ppob/, login/, homepage/
- Naupal: card/, card_reader/, cardreader/
