# Plan: Extract Login-Specific Fields into LoginConfig Interface

**Date:** 2025-10-01  
**Author:** AI Assistant  
**Status:** Pending Approval  
**Revision:** 2 - Using Hilt injection instead of delegation

## User's Initial Prompt

> create a plan to change the "Login specific" fields into a separate LoginConfig interface in a
> separate file and update all usages

**User's Revision Request:**
> revise the plan: instead of update all variant implementations using delegate, remove them.
> provide the LoginConfig to <PERSON><PERSON>, inject them, and use the login config directly

## Overview

This plan outlines the extraction of Login-specific configuration fields from the `VariantConfig`
interface into a separate `LoginConfig` interface. This refactoring will improve separation of
concerns and make the codebase more modular and maintainable.

## Current State Analysis

### Login-Specific Fields in VariantConfig

Currently, there are 4 Login-specific fields in `VariantConfig` (lines 53-56):

```kotlin
// Login specific
@get:ColorRes
val loginTitleTextColor: Int?
val shouldHideTncText: Boolean
val isPartnerLoginTitleStyle: Boolean
val shouldHideBannerLogo: Boolean
```

### Current Usage Locations

1. **LoginActivity.kt** (Primary consumer)
    - Line 79: `variantConfig.isPartnerLoginTitleStyle`
    - Line 83: `variantConfig.shouldHideBannerLogo`
    - Line 86: `variantConfig.loginTitleTextColor`
    - Line 89: `variantConfig.shouldHideBannerLogo`
    - Line 230: `variantConfig.shouldHideTncText`
    - Line 239: `variantConfig.shouldHideTncText`
    - Line 252: `variantConfig.shouldHideTncText`

2. **Variant Implementations** (4 files)
    - `BukuVariantConfig.kt` (lines 56-59)
    - `AtmProVariantConfig.kt` (lines 57-60)
    - `NusaVariantConfig.kt` (lines 57-60)
    - `FakeVariantConfig.kt` (lines 39-42) - Test fixture

### No Test Files Found

No test files currently reference these Login-specific fields directly.

## Proposed Solution

### 1. Create New LoginConfig Interface

**File:** `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/LoginConfig.kt`

```kotlin
package com.bukuwarung.edc.app.config

import androidx.annotation.ColorRes

/**
 * Configuration interface for Login screen behavior and styling
 */
interface LoginConfig {
    /**
     * Color resource for login title text
     * Null means use default color
     */
    @get:ColorRes
    val loginTitleTextColor: Int?
    
    /**
     * Whether to hide Terms & Conditions text in login screen
     */
    val shouldHideTncText: Boolean
    
    /**
     * Whether to use partner-style login title formatting
     * (left-aligned, smaller font size)
     */
    val isPartnerLoginTitleStyle: Boolean
    
    /**
     * Whether to hide the banner logo at top of login screen
     */
    val shouldHideBannerLogo: Boolean
}
```

### 2. Update VariantConfig Interface

**File:** `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/VariantConfig.kt`

**Changes:**

- **REMOVE** the 4 Login-specific field declarations (lines 53-56)
- **REMOVE** the "// Login specific" comment section
- **DO NOT** make `VariantConfig` extend `LoginConfig` (this is the key difference from the previous
  plan)

### 3. Create LoginConfig Implementations for Each Variant

We have 3 main variants + 1 test fixture to update:

#### a. BukuLoginConfig

**File:** `app/src/main/java/com/bukuwarung/edc/app/config/variants/login/BukuLoginConfig.kt`

```kotlin
package com.bukuwarung.edc.app.config.variants.login

import com.bukuwarung.edc.app.config.LoginConfig

class BukuLoginConfig : LoginConfig {
    override val isPartnerLoginTitleStyle: Boolean = false
    override val shouldHideBannerLogo: Boolean = false
    override val loginTitleTextColor: Int? = null
    override val shouldHideTncText: Boolean = false
}
```

#### b. AtmProLoginConfig

**File:** `app/src/main/java/com/bukuwarung/edc/app/config/variants/login/AtmProLoginConfig.kt`

```kotlin
package com.bukuwarung.edc.app.config.variants.login

import com.bukuwarung.edc.R
import com.bukuwarung.edc.app.config.LoginConfig

class AtmProLoginConfig : LoginConfig {
    override val isPartnerLoginTitleStyle: Boolean = true
    override val shouldHideBannerLogo: Boolean = true
    override val loginTitleTextColor: Int = R.color.colorPrimary
    override val shouldHideTncText: Boolean = true
}
```

#### c. NusaLoginConfig

**File:** `app/src/main/java/com/bukuwarung/edc/app/config/variants/login/NusaLoginConfig.kt`

```kotlin
package com.bukuwarung.edc.app.config.variants.login

import com.bukuwarung.edc.app.config.LoginConfig

class NusaLoginConfig : LoginConfig {
    override val isPartnerLoginTitleStyle: Boolean = true
    override val shouldHideBannerLogo: Boolean = true
    override val loginTitleTextColor: Int? = null
    override val shouldHideTncText: Boolean = true
}
```

#### d. FakeLoginConfig (Test Fixture)

**File:** `app/src/testFixtures/java/com/bukuwarung/edc/homepage/ui/home/<USER>

```kotlin
package com.bukuwarung.edc.homepage.ui.home

import com.bukuwarung.edc.app.config.LoginConfig

class FakeLoginConfig : LoginConfig {
    override var isPartnerLoginTitleStyle: Boolean = false
    override var shouldHideBannerLogo: Boolean = false
    override var loginTitleTextColor: Int? = null
    override var shouldHideTncText: Boolean = false
}
```

### 4. Update AppConfig to Include LoginConfig

**File:** `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/AppConfig.kt`

**Changes:**

```kotlin
data class AppConfig(
    val appVariant: AppVariant,
    val appId: AppId,
    val appVersion: AppVersion,
    val networkConfig: NetworkConfig,
    val variantConfig: VariantConfig,
    val loginConfig: LoginConfig,  // ADD this line
    val env: AppEnv
) {
    // ... existing code ...
}
```

### 5. Update AppModule to Provide LoginConfig

**File:** `app/src/main/java/com/bukuwarung/edc/di/AppModule.kt`

**Changes:**

```kotlin
@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    // ... existing code ...

    @Provides
    fun provideVariantConfig(appConfig: AppConfig): VariantConfig = appConfig.variantConfig
    
    @Provides
    fun provideLoginConfig(appConfig: AppConfig): LoginConfig = appConfig.loginConfig  // ADD this
}
```

### 6. Update Variant Config Classes

**IMPORTANT:** Remove all Login-specific field overrides from variant config classes. Do NOT add
delegation or composition.

#### a. BukuVariantConfig

**File:** `app/src/main/java/com/bukuwarung/edc/app/config/variants/BukuVariantConfig.kt`

**Changes:**

- **REMOVE** lines 56-59 (all Login-specific overrides)

#### b. AtmProVariantConfig

**File:** `app/src/main/java/com/bukuwarung/edc/app/config/variants/AtmProVariantConfig.kt`

**Changes:**

- **REMOVE** lines 57-60 (all Login-specific overrides)

#### c. NusaVariantConfig

**File:** `app/src/main/java/com/bukuwarung/edc/app/config/variants/NusaVariantConfig.kt`

**Changes:**

- **REMOVE** lines 57-60 (all Login-specific overrides)

#### d. FakeVariantConfig (Test Fixture)

**File:** `app/src/testFixtures/java/com/bukuwarung/edc/homepage/ui/home/<USER>

**Changes:**

- **REMOVE** lines 39-42 (all Login-specific overrides)

### 7. Update LoginActivity to Inject LoginConfig

**File:** `app/src/main/java/com/bukuwarung/edc/login/ui/LoginActivity.kt`

**Changes:**

```kotlin
@AndroidEntryPoint
class LoginActivity : AppCompatActivity() {

    @Inject
    lateinit var variantConfig: VariantConfig
    
    @Inject
    lateinit var loginConfig: LoginConfig  // ADD this injection

    // ... existing code ...

    override fun onCreate(savedInstanceState: Bundle?) {
        // ... existing code ...
        
        // UPDATE all variantConfig.loginXXX to loginConfig.loginXXX
        if (loginConfig.isPartnerLoginTitleStyle) {  // Changed from variantConfig
            binding.tvLoginTitle.textAlignment = View.TEXT_ALIGNMENT_TEXT_START
            binding.tvLoginTitle.textSize = 20f
        }
        if (loginConfig.shouldHideBannerLogo) {  // Changed from variantConfig
            binding.ivLoginBannerLogo.hideView()
        }
        loginConfig.loginTitleTextColor?.let {  // Changed from variantConfig
            binding.tvLoginTitle.setTextColor(getColorCompat(it))
        }
        if (loginConfig.shouldHideBannerLogo) {  // Changed from variantConfig
            binding.tvLoginSubtitle.hideView()
        }
        
        // ... existing code ...
    }
    
    private fun setTncText() {
        try {
            val ss = SpannableString(getString(R.string.tnc_text))

            ss.setSpan(span1, 36, 57, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            if (loginConfig.shouldHideTncText) {  // Changed from variantConfig
                ss.setSpan(
                    ForegroundColorSpan(getColorCompat(R.color.colorPrimary)),
                    36,
                    57,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            ss.setSpan(span2, 65, 81, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            if (loginConfig.shouldHideTncText) {  // Changed from variantConfig
                ss.setSpan(
                    ForegroundColorSpan(getColorCompat(R.color.colorPrimary)),
                    65,
                    81,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }

            binding.tvLoginTnc.also {
                it?.text = ss
                it?.movementMethod = LinkMovementMethod.getInstance()
            }
            if (loginConfig.shouldHideTncText) {  // Changed from variantConfig
                binding.tvLoginTnc?.hideView()
            }
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }
}
```

**Summary of LoginActivity changes:**

- Add `@Inject lateinit var loginConfig: LoginConfig` field
- Replace 7 occurrences of `variantConfig.loginXXX` with `loginConfig.XXX`
    - Line 79: `variantConfig.isPartnerLoginTitleStyle` → `loginConfig.isPartnerLoginTitleStyle`
    - Line 83: `variantConfig.shouldHideBannerLogo` → `loginConfig.shouldHideBannerLogo`
    - Line 86: `variantConfig.loginTitleTextColor` → `loginConfig.loginTitleTextColor`
    - Line 89: `variantConfig.shouldHideBannerLogo` → `loginConfig.shouldHideBannerLogo`
    - Line 230: `variantConfig.shouldHideTncText` → `loginConfig.shouldHideTncText`
    - Line 239: `variantConfig.shouldHideTncText` → `loginConfig.shouldHideTncText`
    - Line 252: `variantConfig.shouldHideTncText` → `loginConfig.shouldHideTncText`

### 8. Update AppConfig Initialization Points

We need to find where `AppConfig.current` is initialized and update those places to include
`loginConfig`.

**Expected locations to update:**

- Build flavor source sets (likely in `app/src/buku/`, `app/src/atmPro/`, `app/src/nusa/`)
- Application class initialization
- Test setup code

**Search pattern:** Files that instantiate `AppConfig(...)` or set `AppConfig.current`

## Implementation Steps

### Phase 1: Create New Interfaces and Implementations

1. ✅ Create `LoginConfig.kt` interface in `core/domain/app-config`
2. ✅ Create `BukuLoginConfig.kt` in `app/.../variants/login/`
3. ✅ Create `AtmProLoginConfig.kt` in `app/.../variants/login/`
4. ✅ Create `NusaLoginConfig.kt` in `app/.../variants/login/`
5. ✅ Create `FakeLoginConfig.kt` in `app/src/testFixtures/`

### Phase 2: Update Core Config Infrastructure

6. ✅ Update `AppConfig.kt` to add `loginConfig` parameter
7. ✅ Update `AppModule.kt` to provide `LoginConfig`
8. ✅ Update `VariantConfig.kt` to remove Login-specific fields

### Phase 3: Update Variant Implementations

9. ✅ Update `BukuVariantConfig.kt` - remove Login overrides
10. ✅ Update `AtmProVariantConfig.kt` - remove Login overrides
11. ✅ Update `NusaVariantConfig.kt` - remove Login overrides
12. ✅ Update `FakeVariantConfig.kt` - remove Login overrides

### Phase 4: Update AppConfig Initialization

13. ✅ Find and update all `AppConfig(...)` instantiation points to include `loginConfig`
14. ✅ Update flavor-specific initialization code

### Phase 5: Update Consumers

15. ✅ Update `LoginActivity.kt` to inject and use `LoginConfig`

### Phase 6: Validation

16. ✅ Verify no compilation errors
17. ✅ Run `./gradlew allUnitTest` to ensure all tests pass
18. ✅ Manually test login flow for each variant (optional)

## Testing Strategy

### Compilation Testing

- Ensure the project compiles without errors after each phase
- Verify that `LoginActivity` can inject and access `LoginConfig`

### Unit Testing

- Run `./gradlew allUnitTest` after all changes
- Update any tests that directly instantiate `AppConfig` to include `loginConfig` parameter
- No changes to test behavior required

### Manual Testing (Optional)

- Test login screen for BukuAgen variant
- Test login screen for MiniATM Pro variant
- Test login screen for Nusa variant
- Verify styling differences are maintained

## Benefits

1. **True Separation of Concerns**: LoginConfig is completely independent from VariantConfig
2. **Explicit Dependencies**: Login screen clearly shows it needs both configs
3. **Easier Testing**: Can mock LoginConfig independently in tests
4. **Cleaner Architecture**: Each config interface has a single, focused responsibility
5. **Type Safety**: Compile-time guarantee that login config is provided
6. **Flexibility**: Can inject LoginConfig into other components independently

## Risks and Mitigations

| Risk                              | Mitigation                                                    |
|-----------------------------------|---------------------------------------------------------------|
| Breaking existing code            | Update all AppConfig instantiations and usages systematically |
| Test failures                     | Update test fixtures to provide LoginConfig                   |
| Missing AppConfig initializations | Use grep to find all AppConfig instantiations                 |
| Build errors                      | Implement incrementally and verify compilation at each step   |
| Injection failures                | Ensure Hilt module is properly configured                     |

## Comparison: Delegation vs Direct Injection

### Previous Approach (Delegation)

- VariantConfig extends LoginConfig
- Properties delegated to internal LoginConfig instance
- **Pros:** No changes to consuming code, backward compatible
- **Cons:** VariantConfig still knows about Login concerns, delegation overhead

### New Approach (Direct Injection)

- VariantConfig and LoginConfig are separate
- LoginActivity injects both separately
- **Pros:** True separation, explicit dependencies, cleaner architecture
- **Cons:** Requires updating LoginActivity, need to update AppConfig initialization

**Chosen Approach:** Direct Injection - Better long-term architecture despite requiring consumer
updates.

## Files to be Created (5 files)

1. `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/LoginConfig.kt`
2. `app/src/main/java/com/bukuwarung/edc/app/config/variants/login/BukuLoginConfig.kt`
3. `app/src/main/java/com/bukuwarung/edc/app/config/variants/login/AtmProLoginConfig.kt`
4. `app/src/main/java/com/bukuwarung/edc/app/config/variants/login/NusaLoginConfig.kt`
5. `app/src/testFixtures/java/com/bukuwarung/edc/homepage/ui/home/<USER>

## Files to be Modified (9+ files)

1. `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/VariantConfig.kt` - Remove
   Login fields
2. `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/AppConfig.kt` - Add
   loginConfig parameter
3. `app/src/main/java/com/bukuwarung/edc/di/AppModule.kt` - Provide LoginConfig
4. `app/src/main/java/com/bukuwarung/edc/app/config/variants/BukuVariantConfig.kt` - Remove
   overrides
5. `app/src/main/java/com/bukuwarung/edc/app/config/variants/AtmProVariantConfig.kt` - Remove
   overrides
6. `app/src/main/java/com/bukuwarung/edc/app/config/variants/NusaVariantConfig.kt` - Remove
   overrides
7. `app/src/testFixtures/java/com/bukuwarung/edc/homepage/ui/home/<USER>
   overrides
8. `app/src/main/java/com/bukuwarung/edc/login/ui/LoginActivity.kt` - Inject and use LoginConfig
9. **All files that instantiate `AppConfig(...)`** - Add loginConfig parameter (to be discovered)

## Estimated Effort

- **Implementation Time:** 45-60 minutes
- **Finding AppConfig instantiations:** 15 minutes
- **Testing Time:** 15-20 minutes
- **Total:** ~1.5 hours

## Success Criteria

- ✅ All code compiles without errors
- ✅ All unit tests pass (`./gradlew allUnitTest`)
- ✅ `LoginActivity` successfully injects and uses `LoginConfig`
- ✅ Login behavior remains identical for all variants
- ✅ No regression in existing functionality
- ✅ VariantConfig no longer contains Login-specific fields

## Notes

- This refactoring involves **behavioral change in dependency injection** - LoginActivity now
  depends on two separate configs
- The pattern established here can be reused for other config groupings (e.g., `ReceiptConfig`,
  `SettingsConfig`, etc.)
- Consider applying this pattern to other logically-grouped fields in future refactorings
- **Key difference from v1:** No delegation, true separation, explicit dependencies via Hilt
