# Login Button Style Configuration

## User's Initial Prompt

> how to make btn_login like the image
> [Image showing a gradient button with dark gray to pink/red gradient]

> put the configuration into a new style instead and switch it programmatically depending on a
> LoginConfig field (create a new one)

> loginButtonStyle should be a lambda to customize the view instead, like this:
> val loginButtonStyle: ((MaterialButton) -> Unit)?

## Overview

This plan documents the implementation of a configurable login button style system that allows
different app variants to have different button appearances (gradient vs solid color) on the login
screen using a lambda-based customization approach.

## Problem Statement
The login button needed to support different visual styles across app variants:
- Default variants: Yellow solid button (existing `ButtonFill.Yellow`)
- Nusa variant: Gradient button (dark gray to pink/red)

The solution needed to be:
1. Configuration-based (not hardcoded)
2. Variant-specific
3. Flexible to allow any button customization
4. Follow existing architecture patterns

## Solution Architecture

### 1. Lambda-Based Configuration

Instead of using `@StyleRes`, we use a lambda function that accepts a `MaterialButton` and allows
complete customization:

```kotlin
/**
 * Lambda to customize login button appearance
 * Null means use default ButtonFill.Yellow style from layout
 */
var loginButtonStyle: ((MaterialButton) -> Unit)?
```

Benefits of this approach:

- **Maximum flexibility**: Can customize any button property programmatically
- **Context access**: Lambda has access to button's context for resource loading
- **Type safety**: Compile-time checking of button operations
- **Testability**: Easy to mock and test in unit tests

### 2. Gradient Drawable
Created `bg_button_gradient_dark_pink.xml` with state selector:
- **Enabled state**: Horizontal gradient from `#2C2C2C` (dark gray) to `#E91E63` (pink/red)
- **Disabled state**: Solid `@color/black_20` (gray)
- Corner radius: 8dp

### 3. LoginConfig Interface Extension
Added new field to `LoginConfig` interface:
```kotlin
import com.google.android.material.button.MaterialButton

/**
 * Lambda to customize login button appearance
 * Null means use default ButtonFill.Yellow style from layout
 */
var loginButtonStyle: ((MaterialButton) -> Unit)?
```

### 4. Variant Implementations

#### BukuLoginConfig
```kotlin
override var loginButtonStyle: ((MaterialButton) -> Unit)? = null
// Uses default ButtonFill.Yellow from layout
```

#### NusaLoginConfig
```kotlin
override var loginButtonStyle: ((MaterialButton) -> Unit)? = { button ->
    androidx.core.view.ViewCompat.setBackground(
        button,
        ContextCompat.getDrawable(button.context, R.drawable.bg_button_gradient_dark_pink)
    )
    button.setTextColor(ContextCompat.getColor(button.context, R.color.white))
    button.backgroundTintList = null
}
```

#### AtmProLoginConfig
```kotlin
override var loginButtonStyle: ((MaterialButton) -> Unit)? = null
// Uses default ButtonFill.Yellow from layout
```

#### FakeLoginConfig (Test)
```kotlin
override var loginButtonStyle: ((MaterialButton) -> Unit)? = null
// Allows tests to inject custom button styling
```

### 5. LoginActivity Implementation
Applied the style programmatically in `onCreate()`:
```kotlin
// Apply button style from config
loginConfig.loginButtonStyle?.invoke(binding.btnLogin)
```

Simple and clean - just invoke the lambda if it exists!

## Files Modified

### Created

1. `app/src/main/res/drawable/bg_button_gradient_dark_pink.xml` - Gradient drawable with state
   selector

### Modified
1. `core/domain/app-config/src/main/java/com/bukuwarung/edc/app/config/LoginConfig.kt`
   - Added `loginButtonStyle` lambda property

2. `app/src/main/res/values/styles.xml`
   - Added `ButtonFill.Gradient` style (kept for reference, but not used in lambda approach)

3. `app/src/main/java/com/bukuwarung/edc/login/ui/LoginActivity.kt`
   - Added lambda invocation: `loginConfig.loginButtonStyle?.invoke(binding.btnLogin)`

4. `app/src/main/java/com/bukuwarung/edc/app/config/BukuLoginConfig.kt`
   - Implemented `loginButtonStyle` property (null)

5. `app/src/nusacita/java/com/bukuwarung/edc/global/configs/NusaLoginConfig.kt`
   - Implemented `loginButtonStyle` with gradient customization lambda

6. `app/src/atmpro/java/com/bukuwarung/edc/global/configs/AtmProLoginConfig.kt`
   - Implemented `loginButtonStyle` property (null)

7. `app/src/testFixtures/java/com/bukuwarung/edc/homepage/ui/home/<USER>
   - Implemented `loginButtonStyle` property (null, mutable for testing)

8. `app/src/main/res/layout/activity_login.xml`
   - Kept default `ButtonFill.Yellow` style in layout

## Benefits

1. **Maximum Flexibility**: Lambda can perform any customization on the button
2. **Maintainability**: Centralized configuration through LoginConfig
3. **Type Safety**: Direct access to MaterialButton API with compile-time checking
4. **Testability**: Easy to inject custom lambda for testing different styles
5. **Backwards Compatible**: Null value defaults to existing layout behavior
6. **Clean Architecture**: Follows existing variant configuration pattern
7. **No Resource ID Dependencies**: No need for @StyleRes annotations or resource IDs
8. **Context Access**: Lambda can access button.context for loading resources

## Lambda Approach Advantages

Compared to the initial `@StyleRes` approach, the lambda provides:

1. **Dynamic customization**: Can apply logic-based styling
2. **Multiple property changes**: Can modify background, text color, padding, etc. in one place
3. **No intermediate resources**: Direct programmatic access
4. **Easier debugging**: Can add breakpoints and logging inside lambda
5. **Composition**: Can combine multiple styling functions

## Example: Adding More Customizations

With the lambda approach, it's easy to add more customizations:

```kotlin
override var loginButtonStyle: ((MaterialButton) -> Unit)? = { button ->
    // Background
    androidx.core.view.ViewCompat.setBackground(
        button,
        ContextCompat.getDrawable(button.context, R.drawable.bg_button_gradient_dark_pink)
    )
    
    // Text
    button.setTextColor(ContextCompat.getColor(button.context, R.color.white))
    button.textSize = 18f
    button.letterSpacing = 0.1f
    
    // Padding
    button.setPadding(32, 16, 32, 16)
    
    // Other properties
    button.backgroundTintList = null
    button.elevation = 8f
}
```

## Testing Considerations

- Manual testing needed for each variant (Buku, Nusa, AtmPro)
- Verify button appearance in both enabled and disabled states
- Ensure gradient displays correctly on different screen sizes
- Test color contrast for accessibility
- Unit tests can inject custom lambdas to verify button customization

## Future Enhancements

Potential improvements:

1. Extract common button customization functions for reuse
2. Support for animation in button customization
3. Create a ButtonStyleBuilder pattern for complex customizations
4. Add logging/analytics when custom styles are applied

## Notes

- The lambda approach provides maximum flexibility while maintaining clean architecture
- The gradient button uses a horizontal gradient (angle="0")
- The disabled state is handled by the drawable selector
- The implementation is compatible with dependency injection (Hilt)
- Using `var` instead of `val` allows for runtime modification if needed
