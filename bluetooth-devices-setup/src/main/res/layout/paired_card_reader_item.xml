<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    android:background="@drawable/bg_blue_round_rectangle"
    android:backgroundTint="@color/blue_5"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_device"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_card_reader_small" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_device_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:fontFamily="@font/roboto"
        android:gravity="top"
        android:textColor="@color/black_34"
        android:textSize="14sp"
        android:textStyle="normal"
        app:layout_constraintStart_toEndOf="@id/iv_device"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Tianyu MP45_12345" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_device_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:textColor="@color/grey_91"
        android:textSize="12sp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@id/tv_device_name"
        app:layout_constraintTop_toBottomOf="@id/tv_device_name"
        tools:text="86:67:23:00:4A:C2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeviceStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/registered_at_app"
        android:textColor="@color/green"
        android:textSize="10sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/tv_device_address"
        app:layout_constraintTop_toBottomOf="@id/tv_device_address"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_setting"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/vector_setting" />

</androidx.constraintlayout.widget.ConstraintLayout>