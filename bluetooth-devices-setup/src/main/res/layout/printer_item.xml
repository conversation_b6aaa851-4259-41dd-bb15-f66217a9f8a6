<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp"
    android:background="@drawable/bg_corner_8"
    android:paddingBottom="8dp"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_bluetooth"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/vector_bluetooth" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/roboto"
        android:gravity="top"
        android:textColor="@color/black_34"
        android:textSize="14sp"
        android:textStyle="normal"
        app:layout_constraintStart_toEndOf="@id/iv_bluetooth"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Eppos Printer" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:textColor="@color/grey_91"
        android:textSize="12sp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@id/tv_printer_name"
        app:layout_constraintTop_toBottomOf="@id/tv_printer_name"
        tools:text="86:67:23:00:4A:C2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_connect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_bold"
        android:padding="14dp"
        android:text="@string/connect_printer"
        android:textColor="@color/colorPrimary"
        android:textSize="12sp"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="@id/iv_bluetooth"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_bluetooth" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeviceStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/registered_at_app"
        android:textColor="@color/green"
        android:textSize="10sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_printer_address"
        app:layout_constraintTop_toBottomOf="@id/tv_printer_address"
        tools:visibility="visible" />

    <ProgressBar
        android:id="@+id/progress_bar_pair_printer"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginEnd="14dp"
        android:indeterminate="true"
        android:indeterminateTint="@color/colorPrimary"
        android:indeterminateTintMode="src_in"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_bluetooth"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_bluetooth"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
