// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.jetbrains.kotlin.jvm) apply false
    alias(libs.plugins.hilt) apply false
    alias(libs.plugins.google.services) apply false
    alias(libs.plugins.firebase.crashlytics) apply false
    alias(libs.plugins.firebase.perf) apply false
    alias(libs.plugins.ksp) apply false
    alias(libs.plugins.mokkery) apply false
    jacoco
}

// Apply JaCoCo to all projects
allprojects {
    apply(plugin = "jacoco")

    configure<JacocoPluginExtension> {
        toolVersion = "0.8.13"
    }

    // Configure test tasks to generate XML reports
    tasks.withType<Test> {
        reports {
            junitXml.required.set(true)
        }
        testLogging {
            events("passed", "skipped", "failed")
        }
    }
}

subprojects {
    tasks.withType<JacocoReport> {
        dependsOn("test")
        reports {
            xml.required.set(true)
            html.required.set(true)
        }
    }
}

fun dirExists(path: String): Boolean {
    val f = File(path)
    return f.exists() && f.isDirectory
}

fun findNearestParentAndList(path: String) {
    val file = File(path)

    // If the directory exists, just print its contents
    if (file.exists() && file.isDirectory) {
        logger.debug("Directory '$path' exists. Contents:")
        file.listFiles()?.forEach {
            logger.debug("  ${if (it.isDirectory) "DIR " else "FILE"} ${it.name}")
        }
        return
    }

    // If the directory doesn't exist, find the nearest parent that exists
    var currentPath = file
    while (!currentPath.exists() && currentPath.parent != null) {
        currentPath = currentPath.parentFile
    }

    if (currentPath.exists() && currentPath.isDirectory) {
        logger.debug("Directory '$path' doesn't exist.")
        logger.debug("Nearest existing parent directory: '${currentPath.absolutePath}'")
        logger.debug("Contents:")
        currentPath.listFiles()?.forEach {
            logger.debug("  ${if (it.isDirectory) "DIR " else "FILE"} ${it.name}")
        }
    } else {
        logger.debug("Directory '$path' doesn't exist and no parent directory could be found.")
    }
}

data class CoverageModules(
    val module: String,
    val variant: String? = null,
    val isAndroidModule: Boolean = true
) {
    fun getTestTask(): String {
        if (!isAndroidModule) {
            return "$module:testCoverage"
        }
        if (variant != null) {
            return "$module:test${variant}DebugUnitTest"
        }
        return "$module:testDebugUnitTest"
    }

    fun getSourceDir(): String {
        val dir = module.replace(':', '/')
        return "$rootDir$dir/src/main/java"
    }

    fun getClassFilesDir(): String {
        val dir = module.replace(':', '/')
        var projectDir = "$rootDir$dir"
        logger.quiet("rootDir is $rootDir")
        if (dirExists(projectDir)) {
            logger.quiet("projectDir exist")
        } else {
            projectDir = projectDir.replaceFirst("/bukuwarung-edc-app", "")
            dirExists("projectDir replaced and ${dirExists(projectDir)}")
        }
        var output: String
        if (variant != null) {
            val camelCaseVariant = variant.replaceFirstChar {
                if (it.isUpperCase()) {
                    it.lowercase()
                } else {
                    it.toString()
                }
            }
            val buildClassPath = "$projectDir/build/intermediates/classes/"
            val debugClassDir = "transform${variant}DebugClassesWithAsm/dirs"
            output = "$buildClassPath${camelCaseVariant}Debug/$debugClassDir"
            if (dirExists(output)) {
                return output
            } else {
                findNearestParentAndList(output)
            }
        }

        output = "$projectDir/build/tmp/kotlin-classes/debug"
        if (dirExists(output)) {
            return output
        } else {
            findNearestParentAndList(output)
        }

        output = "$projectDir/build/classes/kotlin/main"
        if (dirExists(output)) {
            return output
        } else {
            findNearestParentAndList(output)
        }

        error("no class dir found for module $module")
    }
}

val moduleList = listOf(
    CoverageModules(module = ":app", variant = "BukuDevPlay"),
    CoverageModules(module = ":ui-component"),
    CoverageModules(module = ":bluetooth-devices-setup", variant = "Buku"),
    CoverageModules(module = ":core:data:network"),
    CoverageModules(module = ":core:domain:app-config"),
    CoverageModules(module = ":core:utils:result", isAndroidModule = false)
)

tasks.register<JacocoReport>("allUnitTest") {
    @Suppress("Detekt.SpreadOperator")
    dependsOn(*moduleList.map { it.getTestTask() }.toTypedArray())
}

// Aggregated coverage task
tasks.register<JacocoReport>("jacocoAggregatedReport") {
    dependsOn("allUnitTest")

    reports {
        xml.required.set(true)
        html.required.set(true)
        html.outputLocation.set(
            layout.buildDirectory.dir("reports/jacoco/jacocoAggregatedReport/html")
        )
        xml.outputLocation.set(
            layout.buildDirectory.file(
                "reports/jacoco/jacocoAggregatedReport/jacocoAggregatedReport.xml"
            )
        )
    }

    // Collect execution data from all subprojects
    executionData.setFrom(
        fileTree(rootDir) {
            include("**/build/jacoco/*.exec")
            include("**/build/outputs/unit_test_code_coverage/**/*.exec")
            include("**/build/outputs/code_coverage/**/*.exec")
        }
    )

    val excludes = listOf(
        "**/R.class", "**/R\$*.class", "**/BuildConfig.*", "**/Manifest*.*",
        "**/*Test*.*", "android/**/*.*", "**/*\$Lambda$*.*", "**/*\$inlined$*.*",
        "**/*Module.*", "**/*Dagger*.*", "**/*Hilt*.*", "**/*MembersInjector*.*",
        "**/*_MembersInjector.class", "**/*_Factory*.*", "**/*_Provide*Factory*.*",
        "**/*Extensions*.*", "**/databinding/**", "**/android/databinding/**",
        "**/androidx/databinding/**", "**/BR.*"
    )

    sourceDirectories.setFrom(
        moduleList.map {
            fileTree(it.getSourceDir()) {
                exclude(excludes)
            }
        }
    )

    classDirectories.setFrom(
        moduleList.map {
            fileTree(it.getClassFilesDir()) {
                exclude(excludes)
            }
        }
    )

    doFirst {
        logger.quiet("=== JaCoCo Aggregated Coverage Report ===")
        logger.quiet("Execution data files:")
        executionData.files.forEach { logger.quiet("  - ${it.absolutePath}") }
        logger.quiet("Class directories:")
        classDirectories.files.forEach { logger.quiet("  - ${it.absolutePath}") }
        logger.quiet("Source directories:")
        sourceDirectories.files.forEach { logger.quiet("  - ${it.absolutePath}") }
    }
}
