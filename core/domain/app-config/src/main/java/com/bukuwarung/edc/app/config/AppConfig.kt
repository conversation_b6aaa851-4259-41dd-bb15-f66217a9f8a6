package com.bukuwarung.edc.app.config

import android.util.Size
import com.bukuwarung.edc.app.config.types.AppEnv
import com.bukuwarung.edc.app.config.types.AppId
import com.bukuwarung.edc.app.config.types.AppVariant
import com.bukuwarung.edc.app.config.types.AppVersion

data class AppConfig(
    val appVariant: AppVariant,
    val appId: AppId,
    val appVersion: AppVersion,
    val networkConfig: NetworkConfig,
    val variantConfig: VariantConfig,
    val homeConfig: HomeConfig,
    val loginConfig: LoginConfig,
    val env: AppEnv
) {

    companion object {
        private var _current: AppConfig? = null
        var current: AppConfig
            get() = _current
                ?: error("AppConfig.current has not been initialized")
            set(value) {
                _current = value
            }
    }
}