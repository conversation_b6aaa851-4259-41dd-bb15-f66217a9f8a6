package com.bukuwarung.edc.app.config

import android.util.Size
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes

interface VariantConfig {
    val printerHeaderSize: Size

    // Core variant identifier - single source of truth
    val variantIdentifier: String // "BUK<PERSON>AG<PERSON>", "MINIATMPRO", "NUSA"

    // Business logic properties (computed from variantIdentifier where applicable)
    val appFlow: String

    // Specific API and business logic properties (replacing orderFlowType)
    val isPartnershipForOrderHistory: Boolean // For API calls in EdcOrderHistoryRepo
    val isPartnershipForAnalytics: Boolean // For Analytics.trackSuperProperty()
    val isPartnershipForKycRedirection: Boolean // For KycUtil redirection logic
    val isPartnershipForBluetoothBridge: Boolean // For AppBluetoothBridgeImpl
    val settingsApiOrderFlowType: String // For SettingViewModel API calls - returns "PARTNERSHIP" or "NON_PARTNERSHIP"
    val isPartnershipForTransactionHistory: Boolean // For TransactionHistoryPagingSource API

    // Communication preferences
    val defaultCommunicationChannel: String // "WA" or "SMS"

    // UI/UX properties (specific behaviors)
    val shouldShowHelp: Boolean
    val shouldShowFooterLogo: Boolean
    val shouldShowSwitchOtpChannelButton: Boolean
    val primaryColorRes: Int
    val cardErrorDialogColorRes: Int
    val logoRes: Int
    val receiptLogoRes: Int
    val receiptHeaderText: String
    val shouldShowBusinessAddress: Boolean

    // Receipt/Transaction specific
    val shouldApplyGradientBackground: Boolean
    val shouldShowTransactionFooter: Boolean
    val shouldAddWatermark: Boolean
    val shouldShowReceiptBranding: Boolean // "Dibuat dengan MiniATM BukuAgen"
    val shouldUsePartnerPendingMessagesForTransaction: Boolean

    // Settings specific
    val shouldHideChangePinOption: Boolean
    val shouldHighlightDeviceStatusInSettings: Boolean
    val shouldCheckPinLength: Boolean
    val shouldPerformUserOnboarding: Boolean
    val shouldShowTickerFragment: Boolean
    val shouldHideToolbarMenu: Boolean
    val shouldShowKomisiAgenDialog: Boolean
    val shouldShowDateFilterInHistory: Boolean
    val shouldShowWarrantyConfig: Boolean

    // External service configs
    val apiUrlSuffix: String
    val tncUrl: String
    val privacyPolicyUrl: String

    // Device/activation behavior
    val shouldShowSakuDeviceMessage: Boolean
    val shouldRestrictNonSakuDevices: Boolean
    val shouldShowDeviceActivationBS: Boolean

    // Money transfer & payments
    val shouldShowContactCustomerCare: Boolean
    val shouldShowTransferMoneyHelpButton: Boolean
    val shouldShowBankAccountHelpButton: Boolean

    // Analytics & tracking
    val analyticsAppFlow: String
    val analyticsCommunicationChannel: String
    val analyticsOrderChannel: String // "MiniATMPro" vs "BUKUAGEN"

    // Order & activation flow
    val shouldShowOrderValue: Boolean
    val shouldShowOrderWarning: Boolean
    val shouldShowActivationStepFour: Boolean
    val orderCompletionBehavior: String

    // External pinpad
    val shouldShowExternalPinpadHelpButton: Boolean

    // Bank account colors
    val bankAccountColorRes: Int
    val bankAccountPrimaryColor: Int // BankAccountView

    // Order details screen specific
    val shouldHideOrderHelpButton: Boolean
    val shouldHideGradientBackground: Boolean
    val shouldShowOrderBillingDetails: Boolean
    val edcOrderDetailsVariantType: String // "PARTNERSHIP" vs "NON_PARTNERSHIP" for order type display
    val orderKycRedirectionUrl: String
    val orderKybRedirectionUrl: String
    val shouldShowStepThree: Boolean // KYB step
    val stepFourNumber: String
    val shouldShowRefundForRejectedOrders: Boolean
    val shouldShowRefundHelpButton: Boolean
    val shouldActivateViaSaku: Boolean
    val shouldUseAndroidActivation: Boolean // vs Saku activation

    // SplashActivity specific
    val shouldRedirectToLogin: Boolean // vs WelcomeActivity

    // CardTransactionHistoryActivity specific
    val shouldShowWarrantyNudge: Boolean
    val shouldShowDeviceInfo: Boolean
    val shouldShowBuyEdcButton: Boolean
    val shouldHideFiltersForOrders: Boolean
    val historyScreenTitle: String // "Active device list" vs "EDC order history" vs "Card history"

    // Single-responsibility properties for remaining files
    val shouldShowMoneyTransferHelpButton: Boolean // MoneyTransferConfirmationActivity
    val transactionHistoryDeviceLabel: Int // TransactionHistoryAdapter
    val shouldUseKycFlow: Boolean // KycUtil
    val shouldHideSettlementMenu: Boolean // HomePageRemoteConfig
    val shouldReactivateTerminal: Boolean // TerminalManagementRepository

    // DeeplinkHandlerActivity and DeeplinkHandlerViewModel specific
    val shouldWaitForOpsOnDeeplink: Boolean
    val shouldCheckOrderStatusOnDeeplink: Boolean // DeeplinkHandlerViewModel - whether to check for WAITING_FOR_OPS status

    // Payment related components
    @get:DrawableRes
    val orderInvoiceLogo: Int // OrderInvoice
    val shouldShowOrderInvoiceFooter: Boolean // OrderInvoice
    val shouldShowEducationInSelectBank: Boolean // SelectBankActivity
    val shouldShowBankAccountEducation: Boolean // BankAccountView
    val cardErrorContactText: Int // CardErrorDialog
    val cardErrorButtonColor: Int // CardErrorDialog
    val cardErrorTextColor: Int // CardErrorDialog

    // Receipt printer components
    val usesMiniAtmReceiptLogic: Boolean // Receipt printer components - whether to use MiniATM business logic
    val shouldShowBersamaFooterInReceipt: Boolean
    val receiptBusinessAddress: String

    // UI components
    val tickerFragmentName: String // HomeTickerFragment - direct ticker fragment name ("ticker_fragment_miniatmpro" or "ticker_fragment_edc")
    val shouldShowPromoCodeInHomeTile: Boolean // HomePageTileFragment

    // Settings and profile
    val isPartnershipForProfile: Boolean // ProfileViewModel - whether to skip business name validation
    val profileAnalyticsFlow: String // ProfileViewModel

    // Account and settlement
    val shouldHideSettlementToolbar: Boolean // AddSettlementBankAccountActivity
    val shouldShowAccountEducation: Boolean // ChooseAccountTypeActivity, AddBankAccountMoneyTransferActivity
    val shouldShowSettlementMenu: Boolean // SettlementAccountActivity

    // Card operations
    val shouldShowCardErrorEducation: Boolean // CardPinFirebaseDynamicActivity, ExternalPinpadActivity

    // Filter and history
    val shouldUseDefaultVariantForHistoryFilter: Boolean // DateFilterBottomSheet

    // Authentication and verification
    val verifyOtpAnalyticsFlow: String // VerifyOtpViewModel
    val shouldCheckOnboardingOnLogin: Boolean // LoginViewModel

    // Card activation
    val cardActivationType: String // EdcActivationFragment
}
