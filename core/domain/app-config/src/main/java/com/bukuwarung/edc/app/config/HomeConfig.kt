package com.bukuwarung.edc.app.config

/**
 * Configuration interface for HomePage-specific settings.
 * Contains all configuration fields related to HomePageActivity behavior and UI.
 *
 * This interface is separate from VariantConfig to maintain clear separation of concerns.
 * Components that only need home page configuration can inject this instead of the full VariantConfig.
 */
interface HomeConfig {
    /**
     * Display name for the application shown in the home page title
     */
    val appDisplayName: String

    /**
     * Default store name used when creating accounts or when no business name is available
     */
    val defaultStoreName: String

    /**
     * Controls visibility of the logo (ivLogo) and separator (vwSeparator) in the home page
     */
    val shouldShowLogo: Boolean

    /**
     * Whether the home page should perform account setup on initialization
     */
    val homeShouldPerformAccountSetup: Boolean

    /**
     * Whether the home page should create a saldo account
     */
    val homeShouldCreateSaldoAccount: Boolean

    /**
     * Whether to use dynamic business title in the home page
     */
    val homeShouldUseDynamicBusinessTitle: Boolean

    /**
     * Whether the home page should handle partner order details
     */
    val homeShouldHandlePartnerOrderDetails: Boolean

    /**
     * Whether to show partner activation message
     */
    val homeShouldShowPartnerActivationMessage: Boolean

    /**
     * Whether to show activation bottom sheet
     */
    val homeShouldShowActivationBottomSheet: Boolean

    /**
     * Controls visibility of settings button on card reader
     */
    val shouldShowSettingsButton: Boolean

    /**
     * Whether to use MiniATM Pro ticker styling
     */
    val isUsingMiniatmProTickerStyling: Boolean

    /**
     * Remote config key for homepage schema.
     * Determines which schema configuration to load from Firebase Remote Config.
     *
     * Possible values:
     * - HomePageRemoteConfig.EDC_HOMEPAGE_SCHEMA
     * - HomePageRemoteConfig.EDC_SAKU_HOMEPAGE_SCHEMA
     * - HomePageRemoteConfig.EDC_MINIATMPRO_HOMEPAGE_SCHEMA
     */
    val homepageSchemaKey: String

    /**
     * Remote config key for homepage schema failsafe.
     * Used when the primary homepage schema fails to load.
     *
     * Possible values:
     * - HomePageRemoteConfig.HOMEPAGE_SCHEMA_FAILSAFE (default/Buku)
     * - HomePageRemoteConfig.HOMEPAGE_SCHEMA_FAILSAFE_MINIATMPRO (AtmPro)
     * - HomePageRemoteConfig.HOMEPAGE_SCHEMA_FAILSAFE_NUSACITA (Nusa)
     */
    val homepageSchemaFailsafeKey: String

    /**
     * Variant-specific remote config defaults.
     * Returns a map of remote config keys to their default values for this variant.
     * Used by RemoteConfigUtils to populate the DEFAULTS map.
     *
     * This allows each variant to define its own remote config defaults without
     * RemoteConfigUtils needing to know about all variants.
     */
    val remoteConfigDefaults: Map<String, Any>
}
